<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="description" content="LuckyStar AI聊天助手 - 智能对话、图像生成、语音交互">
    <meta name="keywords" content="AI, 聊天, 助手, 人工智能, 对话, 智能助手, 机器学习">
    <meta name="author" content="LuckyStar Team">
    <meta name="robots" content="index, follow">
    <meta name="theme-color" content="#007bff">
    <meta property="og:title" content="LuckyStar AI聊天助手">
    <meta property="og:description" content="智能对话、图像生成、语音交互的AI助手">
    <meta property="og:type" content="website">
    <meta property="og:url" content="/">
    <meta property="og:image" content="/static/favicon.ico">
    <title>LuckyStar - AI聊天助手</title>
    <!-- 引入外部 CSS - 更新正确的完整性检查 -->
    <link rel="stylesheet"
          href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/github-dark-dimmed.min.css"
          integrity="sha512-zcatBMvxa7rT7dDklfjauWsfiSFParF+hRfCdf4Zr40/MmA1gkFcBRbop0zMpvYF3FmznYFgcL8wlcuO/GwHoA=="
          crossorigin="anonymous"
          referrerpolicy="no-referrer">
    <link rel="stylesheet"
          href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css"
          integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA=="
          crossorigin="anonymous"
          referrerpolicy="no-referrer">
    <link rel="icon" href="/static/favicon.ico" type="image/x-icon">

    <!-- 统一设计系统 -->
    <link rel="stylesheet" href="static/css/design-system.css">

    <!-- 暗黑主题样式 (初始禁用) -->
    <link rel="stylesheet" href="static/css/dark_theme.css" id="dark-theme-style" disabled>

    <!-- 主题过渡优化样式 -->
    <link rel="stylesheet" href="static/css/theme-transitions.css" id="theme-transition-style">

    <!-- 统一的多CDN回退脚本加载器 -->
    <script>
        // 全局脚本加载器 - 统一处理所有外部脚本的多CDN回退加载
        window.ScriptLoader = {
            // 通用脚本加载函数
            loadScript: function(src, callback) {
                const script = document.createElement('script');
                script.src = src;
                script.crossOrigin = 'anonymous';
                script.referrerPolicy = 'no-referrer';
                script.onload = callback;
                script.onerror = function() {
                    console.warn('Failed to load script from:', src);
                    callback(false);
                };
                document.head.appendChild(script);
            },

            // 多CDN回退加载函数
            loadWithFallback: function(sources, checkFunction, globalVarName, onSuccess) {
                function tryLoad(index = 0) {
                    if (index >= sources.length) {
                        console.error(`All ${globalVarName} CDN sources failed`);
                        return;
                    }

                    this.loadScript(sources[index], function(success) {
                        if (success !== false && checkFunction()) {
                            console.log(`${globalVarName} loaded from:`, sources[index]);
                            window[globalVarName + 'Loaded'] = true;
                            if (onSuccess) onSuccess();
                        } else {
                            tryLoad(index + 1);
                        }
                    });
                }
                tryLoad.call(this);
            }
        };

        // 加载 marked.js
        (function() {
            const markedSources = [
                'https://cdnjs.cloudflare.com/ajax/libs/marked/4.3.0/marked.min.js',
                'https://cdn.jsdelivr.net/npm/marked@4.3.0/marked.min.js',
                'https://unpkg.com/marked@4.3.0/marked.min.js'
            ];

            window.ScriptLoader.loadWithFallback(
                markedSources,
                () => typeof marked !== 'undefined',
                'marked'
            );
        })();
    </script>

    <!-- 加载 mermaid.js -->
    <script>
        (function() {
            const mermaidSources = [
                'https://cdnjs.cloudflare.com/ajax/libs/mermaid/10.6.1/mermaid.min.js',
                'https://cdn.jsdelivr.net/npm/mermaid@10.9.0/dist/mermaid.min.js',
                'https://unpkg.com/mermaid@10.9.0/dist/mermaid.min.js'
            ];

            window.ScriptLoader.loadWithFallback(
                mermaidSources,
                () => typeof mermaid !== 'undefined',
                'mermaid'
            );
        })();
    </script>
    
    <script>
        window.BACKEND_DEFAULT_MODEL_NAME = "{{ backend_default_model or 'AI 模型' }}";
    </script>

    <style>
        /* === 设计令牌系统 (Design Tokens) === */
        :root {
            /* === 色彩系统 === */
            /* 主色调 - 更加精致的蓝色系统 */
            --color-primary: #2563eb;
            --color-primary-hover: #1d4ed8;
            --color-primary-active: #1e40af;
            --color-primary-light: #eff6ff;
            --color-primary-dark: #1e3a8a;
            --color-primary-50: #eff6ff;
            --color-primary-100: #dbeafe;
            --color-primary-200: #bfdbfe;
            --color-primary-300: #93c5fd;
            --color-primary-400: #60a5fa;
            --color-primary-500: #3b82f6;
            --color-primary-600: #2563eb;
            --color-primary-700: #1d4ed8;
            --color-primary-800: #1e40af;
            --color-primary-900: #1e3a8a;

            /* === 精细化主色调透明度系统 === */
            --color-primary-alpha-5: rgba(37, 99, 235, 0.05);
            --color-primary-alpha-8: rgba(37, 99, 235, 0.08);
            --color-primary-alpha-10: rgba(37, 99, 235, 0.1);
            --color-primary-alpha-12: rgba(37, 99, 235, 0.12);
            --color-primary-alpha-15: rgba(37, 99, 235, 0.15);
            --color-primary-alpha-20: rgba(37, 99, 235, 0.2);
            --color-primary-alpha-25: rgba(37, 99, 235, 0.25);
            --color-primary-alpha-30: rgba(37, 99, 235, 0.3);
            --color-primary-alpha-40: rgba(37, 99, 235, 0.4);
            --color-primary-alpha-50: rgba(37, 99, 235, 0.5);

            /* 语义色彩 */
            --color-success: #28a745;
            --color-success-hover: #218838;
            --color-success-light: #d4edda;
            --color-warning: #ffc107;
            --color-warning-hover: #e0a800;
            --color-warning-light: #fff3cd;
            --color-danger: #dc3545;
            --color-danger-hover: #c82333;
            --color-danger-light: #f8d7da;
            --color-info: #17a2b8;
            --color-info-hover: #138496;
            --color-info-light: #d1ecf1;

            /* 中性色彩 - 更加细腻的灰度系统 */
            --color-gray-25: #fcfcfd;
            --color-gray-50: #f9fafb;
            --color-gray-100: #f2f4f7;
            --color-gray-200: #eaecf0;
            --color-gray-300: #d0d5dd;
            --color-gray-400: #98a2b3;
            --color-gray-500: #667085;
            --color-gray-600: #475467;
            --color-gray-700: #344054;
            --color-gray-800: #1d2939;
            --color-gray-900: #101828;
            --color-gray-950: #0c111d;

            /* 背景色彩 - 层次化背景系统 */
            --bg-primary: #ffffff;
            --bg-secondary: #fcfcfd;
            --bg-tertiary: #f9fafb;
            --bg-quaternary: #f2f4f7;
            --bg-overlay: rgba(15, 23, 42, 0.4);
            --bg-surface: #ffffff;
            --bg-elevated: #ffffff;
            --bg-canvas: #fafbfc;
            --bg-subtle: #f8fafc;
            --bg-muted: #f1f5f9;

            /* 文字色彩 */
            --text-primary: #212529;
            --text-secondary: #495057;
            --text-tertiary: #6c757d;
            --text-muted: #adb5bd;
            --text-inverse: #ffffff;
            --text-link: #007bff;
            --text-link-hover: #0056b3;

            /* 边框色彩 */
            --border-primary: #dee2e6;
            --border-secondary: #e9ecef;
            --border-tertiary: #f4f6f8;
            --border-error: #dc3545;
            --border-success: #28a745;

            /* === 现代化聚焦效果系统 === */
            /* 亮色主题聚焦色彩 - 柔和淡蓝色系 */
            --focus-border-color: #64b5f6;
            --focus-glow-color: rgba(100, 181, 246, 0.25);
            --focus-glow-radius: 3px;
            --focus-transition: all 250ms cubic-bezier(0, 0, 0.2, 1);

            /* === 间距系统 - 黄金比例间距 === */
            --spacing-xs: 0.375rem;   /* 6px */
            --spacing-sm: 0.5rem;     /* 8px - 优化按钮间距 */
            --spacing-md: 0.75rem;    /* 12px - 黄金比例间距 */
            --spacing-lg: 1rem;       /* 16px */
            --spacing-xl: 1.5rem;     /* 24px */
            --spacing-2xl: 2rem;      /* 32px */
            --spacing-3xl: 3rem;      /* 48px */
            --spacing-4xl: 4rem;      /* 64px */
            --spacing-5xl: 6rem;      /* 96px */

            /* 专用间距 - 精确控制 */
            --spacing-button-gap: 0.5rem;      /* 8px - 按钮间距 */
            --spacing-toolbar-padding: 0.75rem; /* 12px - 工具栏内边距 */
            --spacing-input-padding: 1rem;      /* 16px - 输入框内边距 */

            /* === 字体系统 === */
            --font-family-base: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", sans-serif;
            --font-family-mono: 'JetBrains Mono', 'Fira Code', 'Cascadia Code', Consolas, Monaco, 'Andale Mono', monospace;

            /* === 字体渲染优化 === */
            --font-rendering-optimized: optimizeLegibility;
            --text-rendering-crisp: geometricPrecision;

            --font-size-xs: 0.75rem;   /* 12px */
            --font-size-sm: 0.875rem;  /* 14px */
            --font-size-base: 1rem;    /* 16px */
            --font-size-lg: 1.125rem;  /* 18px */
            --font-size-xl: 1.25rem;   /* 20px */
            --font-size-2xl: 1.5rem;   /* 24px */
            --font-size-3xl: 1.875rem; /* 30px */

            --font-weight-normal: 400;
            --font-weight-medium: 500;
            --font-weight-semibold: 600;
            --font-weight-bold: 700;

            --line-height-tight: 1.25;
            --line-height-normal: 1.5;
            --line-height-relaxed: 1.75;

            /* === 圆角系统 === */
            --radius-none: 0;
            --radius-sm: 0.25rem;      /* 4px */
            --radius-base: 0.375rem;   /* 6px */
            --radius-md: 0.5rem;       /* 8px */
            --radius-lg: 0.75rem;      /* 12px */
            --radius-xl: 1rem;         /* 16px */
            --radius-2xl: 1.5rem;      /* 24px */
            --radius-full: 9999px;

            /* === 阴影系统 - 精致层次化 === */
            --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.04);
            --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.06), 0 1px 2px rgba(0, 0, 0, 0.04);
            --shadow-base: 0 1px 3px rgba(0, 0, 0, 0.06), 0 1px 2px rgba(0, 0, 0, 0.04);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.08), 0 2px 4px -1px rgba(0, 0, 0, 0.04);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.08), 0 4px 6px -2px rgba(0, 0, 0, 0.04);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.08), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.12);
            --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.04);
            --shadow-focus: 0 0 0 3px rgba(59, 130, 246, 0.12);
            --shadow-glow: 0 0 20px rgba(59, 130, 246, 0.15);

            /* 专用阴影 - 输入区域 */
            --shadow-input-container: 0 2px 8px rgba(0, 0, 0, 0.04), 0 1px 3px rgba(0, 0, 0, 0.06);
            --shadow-input-focus: 0 4px 12px rgba(0, 0, 0, 0.08); /* 移除蓝色外围发光 */
            --shadow-button-hover: 0 2px 4px rgba(0, 0, 0, 0.08), 0 1px 2px rgba(0, 0, 0, 0.04);
            --shadow-button-active: 0 1px 2px rgba(0, 0, 0, 0.08);

            /* === 动画系统 === */

            /* 动画时长 */
            --duration-instant: 0ms;
            --duration-fast: 150ms;
            --duration-base: 200ms;
            --duration-medium: 300ms;
            --duration-slow: 500ms;
            --duration-slower: 750ms;
            --duration-slowest: 1000ms;

            /* 缓动函数 - 自然的动效曲线 */
            --ease-linear: linear;
            --ease-in: cubic-bezier(0.4, 0, 1, 1);
            --ease-out: cubic-bezier(0, 0, 0.2, 1);
            --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
            --ease-back: cubic-bezier(0.68, -0.55, 0.265, 1.55);
            --ease-elastic: cubic-bezier(0.68, -0.6, 0.32, 1.6);
            --ease-bounce: cubic-bezier(0.175, 0.885, 0.32, 1.275);
            --ease-smooth: cubic-bezier(0.25, 0.46, 0.45, 0.94);
            --ease-sharp: cubic-bezier(0.4, 0, 0.6, 1);
            --ease-spring: cubic-bezier(0.175, 0.885, 0.32, 1.275);
            --ease-gentle: cubic-bezier(0.25, 0.46, 0.45, 0.94);
            --ease-natural: cubic-bezier(0.4, 0.0, 0.2, 1);
            --ease-emphasized: cubic-bezier(0.2, 0.0, 0, 1.0);
            --ease-expressive: cubic-bezier(0.4, 0.0, 0.1, 1);

            /* 动画延迟 */
            --delay-none: 0ms;
            --delay-short: 50ms;
            --delay-medium: 100ms;
            --delay-long: 200ms;

            /* === 统一z-index层级系统 === */
            --z-background: -1;
            --z-base: 0;
            --z-content: 1;
            --z-elevated: 2;
            --z-floating: 3;
            --z-overlay: 4;
            --z-modal: 5;
            --z-tooltip: 6;
            --z-notification: 7;
            --z-maximum: 9999;

            /* === 性能优化设置 === */
            /* 启用硬件加速 */
            transform: translateZ(0);
            /* 优化重绘性能 */
            backface-visibility: hidden;
            /* 优化字体渲染 */
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;

            /* === Z-index 层级系统 === */
            --z-dropdown: 1000;
            --z-sticky: 1020;
            --z-sidebar: 1025;
            --z-fixed: 1030;
            --z-modal-backdrop: 1040;
            --z-modal: 1050;
            --z-popover: 1060;
            --z-tooltip: 1070;
            --z-toast: 1080;

            /* === 响应式断点系统 === */
            --breakpoint-xs: 480px;
            --breakpoint-sm: 640px;
            --breakpoint-md: 768px;
            --breakpoint-lg: 1024px;
            --breakpoint-xl: 1280px;
            --breakpoint-2xl: 1536px;
        }

        /* === 基础样式重置 === */
        html {
            height: 100%;
            overflow: hidden;
            font-size: 16px; /* 使用标准的16px基准，确保rem单位计算正确 */
            scroll-behavior: smooth;
        }

        body {
            font-family: var(--font-family-base);
            font-size: var(--font-size-sm);
            line-height: var(--line-height-normal);
            color: var(--text-primary);
            background: var(--bg-primary);
            margin: 0;
            padding: 0;
            height: 100%;
            display: flex;
            flex-direction: column;
            position: relative;
            overflow: hidden;
            transition: background var(--duration-base) var(--ease-out),
                        color var(--duration-base) var(--ease-out);
        }

        #app-container {
            display: flex;
            flex-direction: column;
            height: 100%;
            overflow: hidden;
        }

        /* === 通用样式重置与优化 === */

        /* 移除移动端点击高亮 */
        * {
            -webkit-tap-highlight-color: transparent;
            box-sizing: border-box;
            /* 字体渲染优化 - 确保所有文本清晰显示 */
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: var(--font-rendering-optimized);
            font-feature-settings: "liga" 1, "kern" 1;
            font-variant-ligatures: common-ligatures;
        }

        /* === 主题切换平滑过渡效果 - 优化版 === */
        body {
            transition: background-color var(--theme-transition-duration) var(--theme-transition-easing),
                       color var(--theme-transition-duration) var(--theme-transition-easing),
                       background-image var(--theme-transition-duration) var(--theme-transition-easing);
            will-change: background-color, color;
        }

        /* 过渡状态优化 */
        body.theme-transitioning {
            /* 确保过渡期间的视觉稳定性 */
            overflow-x: hidden;
        }

        /* 为主要容器添加过渡效果 */
        #sidebar,
        #main-content,
        #input-container,
        .message-container,
        .custom-selector-container,
        .custom-selector-panel,
        header,
        #conversation-options-bar,
        #input-textarea-wrapper,
        .top-action-button,
        .message-action-button,
        .custom-selector-trigger {
            transition: background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                       border-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                       box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                       color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* 可交互元素的点击高亮移除 */
        button, a,
        .session-item,
        .custom-selector-trigger,
        .custom-selector-option,
        .input-action-button,
        .top-action-button,
        .sidebar-io-button,
        .message-action-button {
            -webkit-tap-highlight-color: transparent;
        }

        /* === 滚动条差异化处理系统 === */

        /* 全局滚动条隐藏 - 除主聊天区域外 */
        ::-webkit-scrollbar {
            display: none;
        }

        /* Firefox 全局滚动条隐藏 */
        * {
            scrollbar-width: none;
        }

        /* === 主聊天区域专用滚动条样式 === */
        /* 仅主聊天区域保留滚动条，隐藏箭头 */
        #chatbox::-webkit-scrollbar {
            display: block;
            width: 14px;
        }

        #chatbox::-webkit-scrollbar-button {
            display: none;
        }

        #chatbox::-webkit-scrollbar-track {
            background: transparent;
            border-radius: 8px;
            margin: 4px 0;
        }

        #chatbox::-webkit-scrollbar-thumb {
            background: rgba(0, 0, 0, 0.12);
            border-radius: 8px;
            border: 3px solid transparent;
            background-clip: content-box;
            transition: all var(--duration-medium) var(--ease-natural);
            min-height: 50px;
        }

        #chatbox::-webkit-scrollbar-thumb:hover {
            background: rgba(0, 0, 0, 0.2);
            border-width: 2px;
        }

        #chatbox::-webkit-scrollbar-thumb:active {
            background: rgba(0, 0, 0, 0.3);
            border-width: 1px;
        }

        #chatbox::-webkit-scrollbar-corner {
            background: transparent;
        }

        /* Firefox 主聊天区域滚动条 */
        #chatbox {
            scrollbar-width: thin;
            scrollbar-color: rgba(0, 0, 0, 0.12) transparent;
        }

        /* === 深色模式滚动条适配 === */
        @media (prefers-color-scheme: dark) {
            /* 仅主聊天区域深色模式滚动条 */
            #chatbox::-webkit-scrollbar-thumb {
                background: rgba(255, 255, 255, 0.15);
            }

            #chatbox::-webkit-scrollbar-thumb:hover {
                background: rgba(255, 255, 255, 0.25);
            }

            #chatbox::-webkit-scrollbar-thumb:active {
                background: rgba(255, 255, 255, 0.35);
            }

            #chatbox::-webkit-scrollbar-track {
                background: transparent;
            }

            /* Firefox 主聊天区域深色模式 */
            #chatbox {
                scrollbar-color: rgba(255, 255, 255, 0.15) transparent;
            }
        }

        /* === 表单元素通用样式 === */

        /* === 文本域现代化滚动条样式 === */
        textarea,
        .message-bubble .message-content[contenteditable="true"] {
            resize: none !important;
            overflow-y: auto;
            scrollbar-width: thin; /* Firefox - 显示细滚动条 */
            scrollbar-color: rgba(0, 0, 0, 0.1) transparent;
            min-height: 24px;
            line-height: var(--line-height-normal);
            font-family: inherit;
            border: none;
            outline: none;
            background: transparent;
        }

        /* 输入框滚动条隐藏 - 已通过全局设置处理 */

        /* === 滚动条显示策略优化 === */
        /* 完全隐藏所有输入框的滚动条 */
        #message-input::-webkit-scrollbar,
        textarea::-webkit-scrollbar,
        input::-webkit-scrollbar,
        .message-bubble .message-content[contenteditable="true"]::-webkit-scrollbar {
            display: none !important;
        }

        /* Firefox 输入框滚动条隐藏 */
        #message-input,
        textarea,
        input,
        .message-bubble .message-content[contenteditable="true"] {
            scrollbar-width: none !important;
        }

        /* 输入框通用样式 */
        input, textarea, select {
            font-family: inherit;
            font-size: inherit;
            line-height: inherit;
        }

        /* 按钮通用样式 */
        button {
            font-family: inherit;
            font-size: inherit;
            line-height: inherit;
            cursor: pointer;
            border: none;
            background: none;
            padding: 0;
            margin: 0;
            outline: none;
            transition: all var(--duration-fast) var(--ease-out);
        }

        button:disabled {
            cursor: not-allowed;
            opacity: 0.6;
        }

        /* 链接样式 */
        a {
            color: var(--text-link);
            text-decoration: none;
            transition: color var(--duration-fast) var(--ease-out);
        }

        a:hover {
            color: var(--text-link-hover);
            text-decoration: underline;
        }

        /* 图片样式 */
        img {
            max-width: 100%;
            height: auto;
            display: block;
        }

        /* === 现代商业级按键设计系统 (2024) === */
        /* 参考 GitHub、VS Code、Figma、Notion 等热门应用设计规范 */

        /* 按钮基础样式 */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            padding: 6px 12px;
            border: 0.5px solid var(--border-primary);
            border-radius: 6px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Noto Sans', Helvetica, Arial, sans-serif;
            font-size: 13px;
            font-weight: 500;
            line-height: 20px;
            text-decoration: none;
            cursor: pointer;
            user-select: none;
            white-space: nowrap;
            vertical-align: middle;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            background-color: var(--bg-primary);
            color: var(--text-primary);
        }

        /* 按钮焦点效果 - 仅键盘导航显示 */
        .btn:focus {
            outline: none;
        }

        .btn:focus-visible {
            outline: 2px solid var(--color-primary);
            outline-offset: 2px;
        }

        .btn:disabled {
            cursor: not-allowed;
            opacity: 0.5;
            pointer-events: none;
        }

        /* 按钮尺寸变体 - 现代化精致设计 */
        .btn-xs {
            padding: 4px 8px;
            font-size: 11px;
            line-height: 16px;
            border-radius: 4px;
        }

        .btn-sm {
            padding: 5px 10px;
            font-size: 12px;
            line-height: 18px;
            border-radius: 5px;
        }

        .btn-lg {
            padding: 8px 16px;
            font-size: 14px;
            line-height: 22px;
            border-radius: 7px;
        }

        .btn-xl {
            padding: 10px 20px;
            font-size: 15px;
            line-height: 24px;
            border-radius: 8px;
        }

        /* === 现代化按钮样式系统 === */

        /* 主要按钮 - GitHub/VS Code 风格 */
        .btn-primary {
            background: var(--color-primary);
            border-color: var(--color-primary);
            color: white;
            font-weight: 500;
        }

        .btn-primary:hover:not(:disabled) {
            background: var(--color-primary-hover);
            border-color: var(--color-primary-hover);
            transform: translateY(-0.5px);
        }

        .btn-primary:active:not(:disabled) {
            background: var(--color-primary-active);
            border-color: var(--color-primary-active);
            transform: translateY(0);
        }

        /* 次要按钮 - 现代扁平设计 */
        .btn-secondary {
            background: var(--color-gray-100);
            border-color: var(--color-gray-300);
            color: var(--text-primary);
            font-weight: 500;
        }

        .btn-secondary:hover:not(:disabled) {
            background: var(--color-gray-200);
            border-color: var(--color-gray-400);
            transform: translateY(-0.5px);
        }

        .btn-secondary:active:not(:disabled) {
            background: var(--color-gray-300);
            border-color: var(--color-gray-500);
            transform: translateY(0);
        }

        /* 轮廓按钮 - 现代边框设计 */
        .btn-outline {
            background: transparent;
            border: 0.5px solid var(--color-primary);
            color: var(--color-primary);
            font-weight: 500;
        }

        .btn-outline:hover:not(:disabled) {
            background: var(--color-primary);
            border-color: var(--color-primary);
            color: white;
            transform: translateY(-0.5px);
        }

        .btn-outline:active:not(:disabled) {
            background: var(--color-primary-active);
            border-color: var(--color-primary-active);
            color: white;
            transform: translateY(0);
        }

        /* 幽灵按钮 - 微妙交互 */
        .btn-ghost {
            background: transparent;
            border: 0.5px solid transparent;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .btn-ghost:hover:not(:disabled) {
            background: var(--color-gray-100);
            color: var(--text-primary);
            border-color: var(--color-gray-300);
            transform: translateY(-0.5px);
        }

        .btn-ghost:active:not(:disabled) {
            background: var(--color-gray-200);
            border-color: var(--color-gray-400);
            transform: translateY(0);
        }

        /* 按钮形状变体 - 现代化设计 */
        .btn-round {
            border-radius: 50px;
        }

        .btn-square {
            border-radius: 2px;
        }

        .btn-icon {
            width: 32px;
            height: 32px;
            padding: 0;
            border-radius: 6px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .btn-icon.btn-sm {
            width: 28px;
            height: 28px;
            border-radius: 5px;
        }

        .btn-icon.btn-lg {
            width: 36px;
            height: 36px;
            border-radius: 7px;
        }

        /* === 状态按钮样式 - 现代扁平设计 === */

        .btn-success {
            background: var(--color-success);
            border-color: var(--color-success);
            color: white;
            font-weight: 500;
        }

        .btn-success:hover:not(:disabled) {
            background: var(--color-success-hover);
            border-color: var(--color-success-hover);
            transform: translateY(-0.5px);
        }

        .btn-warning {
            background: var(--color-warning);
            border-color: var(--color-warning);
            color: white;
            font-weight: 500;
        }

        .btn-warning:hover:not(:disabled) {
            background: var(--color-warning-hover);
            border-color: var(--color-warning-hover);
            transform: translateY(-0.5px);
        }

        .btn-danger {
            background: var(--color-danger);
            border-color: var(--color-danger);
            color: white;
            font-weight: 500;
        }

        .btn-danger:hover:not(:disabled) {
            background: var(--color-danger-hover);
            border-color: var(--color-danger-hover);
            transform: translateY(-0.5px);
        }

        /* 信息按钮样式 */
        .btn-info {
            background: var(--color-info);
            border-color: var(--color-info);
            color: white;
            font-weight: 500;
        }

        .btn-info:hover:not(:disabled) {
            background: var(--color-info-hover);
            border-color: var(--color-info-hover);
            transform: translateY(-0.5px);
        }

        /* 强调按钮样式 */
        .btn-accent {
            background: var(--color-accent);
            border-color: var(--color-accent);
            color: white;
            font-weight: 500;
        }

        .btn-accent:hover:not(:disabled) {
            background: var(--color-accent-hover);
            border-color: var(--color-accent-hover);
            transform: translateY(-0.5px);
        }

        /* === 输入框组件样式 === */

        /* === 现代化输入框样式 === */

        .form-control {
            display: block;
            width: 100%;
            padding: var(--spacing-sm) var(--spacing-md);
            font-family: inherit;
            font-size: var(--font-size-sm);
            font-weight: var(--font-weight-normal);
            line-height: var(--line-height-normal);
            color: var(--text-primary);
            background:
                linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.95) 100%);
            background-clip: padding-box;
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-base);
            transition: all var(--duration-fast) var(--ease-out);
            position: relative;
            backdrop-filter: blur(10px);
            box-shadow:
                0 1px 3px rgba(0, 0, 0, 0.04),
                inset 0 1px 0 rgba(255, 255, 255, 0.7);
        }

        .form-control::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg,
                rgba(79, 70, 229, 0.02) 0%,
                transparent 50%,
                rgba(6, 182, 212, 0.02) 100%);
            border-radius: inherit;
            opacity: 0;
            transition: opacity var(--duration-fast) var(--ease-out);
            pointer-events: none;
        }

        .form-control:focus {
            color: var(--text-primary);
            background:
                linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(240, 240, 255, 0.98) 100%);
            border-color: var(--color-primary);
            border-width: 1.5px;
            outline: none;
            /* 简化阴影效果，避免边角锯齿 */
            box-shadow:
                0 0 0 2px rgba(79, 70, 229, 0.12),
                0 4px 12px rgba(79, 70, 229, 0.08),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
            transform: translateY(-1px) translateZ(0);
            backdrop-filter: blur(15px);
            /* 优化边框渲染 */
            border-style: solid;
            box-sizing: border-box;
        }

        .form-control:focus::before {
            opacity: 1;
        }

        .form-control:disabled,
        .form-control[readonly] {
            background-color: var(--color-gray-100);
            color: var(--text-muted);
            opacity: 0.7;
            cursor: not-allowed;
        }

        .form-control::placeholder {
            color: var(--text-muted);
            opacity: 1;
        }

        /* 输入框尺寸变体 */
        .form-control-sm {
            padding: var(--spacing-xs) var(--spacing-sm);
            font-size: var(--font-size-xs);
            border-radius: var(--radius-sm);
        }

        .form-control-lg {
            padding: var(--spacing-md) var(--spacing-lg);
            font-size: var(--font-size-base);
            border-radius: var(--radius-md);
        }

        /* 输入框状态变体 */
        .form-control.is-valid {
            border-color: var(--border-success);
        }

        .form-control.is-valid:focus {
            border-color: var(--border-success);
            box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.25);
        }

        .form-control.is-invalid {
            border-color: var(--border-error);
        }

        .form-control.is-invalid:focus {
            border-color: var(--border-error);
            box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.25);
        }

        /* 文本域样式 */
        .form-control.textarea {
            min-height: 80px;
            resize: vertical;
        }

        /* 选择框样式 */
        .form-select {
            display: block;
            width: 100%;
            padding: var(--spacing-sm) var(--spacing-2xl) var(--spacing-sm) var(--spacing-md);
            font-family: inherit;
            font-size: var(--font-size-sm);
            font-weight: var(--font-weight-normal);
            line-height: var(--line-height-normal);
            color: var(--text-primary);
            background-color: var(--bg-primary);
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right var(--spacing-md) center;
            background-size: 16px 12px;
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-base);
            transition: all var(--duration-fast) var(--ease-out);
            appearance: none;
            cursor: pointer;
        }

        .form-select:focus {
            outline: none;
            border-color: var(--focus-border-color);
            box-shadow: 0 0 0 var(--focus-glow-radius) var(--focus-glow-color);
            transition: var(--focus-transition);
        }

        .form-select:disabled {
            background-color: var(--color-gray-100);
            color: var(--text-muted);
            cursor: not-allowed;
        }

        /* === 模态框组件样式 === */

        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: var(--bg-overlay);
            backdrop-filter: blur(4px);
            z-index: var(--z-modal-backdrop);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: var(--spacing-lg);
            opacity: 0;
            visibility: hidden;
            transition: all var(--duration-base) var(--ease-out);
        }

        .modal-overlay.visible {
            opacity: 1;
            visibility: visible;
        }

        .modal-dialog {
            background-color: var(--bg-elevated);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-2xl);
            max-width: 500px;
            width: 100%;
            max-height: 90vh;
            display: flex;
            flex-direction: column;
            transform: scale(0.95) translateY(20px);
            transition: transform var(--duration-base) var(--ease-out);
            position: relative;
        }

        .modal-overlay.visible .modal-dialog {
            transform: scale(1) translateY(0);
        }

        .modal-header {
            padding: var(--spacing-xl) var(--spacing-xl) var(--spacing-lg) var(--spacing-xl);
            border-bottom: 1px solid var(--border-secondary);
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex-shrink: 0;
        }

        .modal-title {
            margin: 0;
            font-size: var(--font-size-xl);
            font-weight: var(--font-weight-semibold);
            color: var(--text-primary);
            line-height: var(--line-height-tight);
        }

        .modal-close {
            background: none;
            border: none;
            color: var(--text-tertiary);
            font-size: var(--font-size-xl);
            cursor: pointer;
            padding: var(--spacing-xs);
            border-radius: var(--radius-sm);
            transition: all var(--duration-fast) var(--ease-out);
            display: flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
        }

        .modal-close:hover {
            background-color: var(--color-gray-200);
            color: var(--text-primary);
        }

        .modal-close:focus-visible {
            outline: none;
            border-color: var(--focus-border-color);
            box-shadow: 0 0 0 var(--focus-glow-radius) var(--focus-glow-color);
            transition: var(--focus-transition);
        }

        .modal-body {
            padding: var(--spacing-xl);
            flex-grow: 1;
            overflow-y: auto;
        }

        .modal-footer {
            padding: var(--spacing-lg) var(--spacing-xl) var(--spacing-xl) var(--spacing-xl);
            border-top: 1px solid var(--border-secondary);
            display: flex;
            align-items: center;
            justify-content: flex-end;
            gap: var(--spacing-md);
            flex-shrink: 0;
        }

        /* 模态框尺寸变体 */
        .modal-dialog-sm {
            max-width: 300px;
        }

        .modal-dialog-lg {
            max-width: 800px;
        }

        .modal-dialog-xl {
            max-width: 1140px;
        }

        .modal-dialog-fullscreen {
            max-width: none;
            width: 100vw;
            height: 100vh;
            max-height: none;
            margin: 0;
            border-radius: 0;
        }

        /* === 卡片组件样式 === */

        .card {
            background-color: var(--bg-elevated);
            border: 1px solid var(--border-secondary);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-sm);
            overflow: hidden;
            transition: all var(--duration-fast) var(--ease-out);
        }

        .card:hover {
            box-shadow: var(--shadow-md);
            transform: translateY(-1px);
        }

        .card-header {
            padding: var(--spacing-lg) var(--spacing-xl);
            background-color: var(--bg-secondary);
            border-bottom: 1px solid var(--border-secondary);
        }

        .card-body {
            padding: var(--spacing-xl);
        }

        .card-footer {
            padding: var(--spacing-lg) var(--spacing-xl);
            background-color: var(--bg-secondary);
            border-top: 1px solid var(--border-secondary);
        }

        .card-title {
            margin: 0 0 var(--spacing-sm) 0;
            font-size: var(--font-size-lg);
            font-weight: var(--font-weight-semibold);
            color: var(--text-primary);
        }

        .card-text {
            margin: 0;
            color: var(--text-secondary);
            line-height: var(--line-height-relaxed);
        }

        /* === 徽章组件样式 === */

        .badge {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: var(--spacing-xs) var(--spacing-sm);
            font-size: var(--font-size-xs);
            font-weight: var(--font-weight-semibold);
            line-height: 1;
            color: white;
            text-align: center;
            white-space: nowrap;
            vertical-align: baseline;
            border-radius: var(--radius-sm);
            background-color: var(--color-gray-600);
            transition: all var(--duration-fast) var(--ease-out);
        }

        .badge-primary {
            background-color: var(--color-primary);
        }

        .badge-secondary {
            background-color: var(--color-gray-600);
        }

        .badge-success {
            background-color: var(--color-success);
        }

        .badge-warning {
            background-color: var(--color-warning);
        }

        .badge-danger {
            background-color: var(--color-danger);
        }

        .badge-info {
            background-color: var(--color-info);
        }

        .badge-light {
            background-color: var(--color-gray-200);
            color: var(--text-primary);
        }

        .badge-dark {
            background-color: var(--color-gray-800);
        }

        /* 徽章尺寸变体 */
        .badge-sm {
            padding: 2px var(--spacing-xs);
            font-size: 10px;
        }

        .badge-lg {
            padding: var(--spacing-sm) var(--spacing-md);
            font-size: var(--font-size-sm);
        }

        /* 徽章形状变体 */
        .badge-pill {
            border-radius: var(--radius-full);
        }

        .badge-square {
            border-radius: 0;
        }

        /* === 标签组件样式 === */

        .tag {
            display: inline-flex;
            align-items: center;
            gap: var(--spacing-xs);
            padding: var(--spacing-xs) var(--spacing-sm);
            font-size: var(--font-size-xs);
            font-weight: var(--font-weight-medium);
            line-height: var(--line-height-tight);
            color: var(--text-primary);
            background-color: var(--color-gray-200);
            border: 1px solid var(--border-secondary);
            border-radius: var(--radius-base);
            transition: all var(--duration-fast) var(--ease-out);
        }

        .tag:hover {
            background-color: var(--color-gray-300);
            border-color: var(--border-primary);
        }

        .tag-removable {
            cursor: pointer;
        }

        .tag-remove {
            background: none;
            border: none;
            color: var(--text-tertiary);
            font-size: var(--font-size-xs);
            cursor: pointer;
            padding: 0;
            margin-left: var(--spacing-xs);
            border-radius: var(--radius-sm);
            transition: color var(--duration-fast) var(--ease-out);
        }

        .tag-remove:hover {
            color: var(--color-danger);
        }

        /* === 分隔线组件样式 === */

        .divider {
            border: none;
            height: 1px;
            background-color: var(--border-secondary);
            margin: var(--spacing-lg) 0;
        }

        .divider-vertical {
            width: 1px;
            height: auto;
            background-color: var(--border-secondary);
            margin: 0 var(--spacing-lg);
            display: inline-block;
        }

        .divider-text {
            position: relative;
            text-align: center;
            margin: var(--spacing-xl) 0;
        }

        .divider-text::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background-color: var(--border-secondary);
        }

        .divider-text span {
            background-color: var(--bg-primary);
            padding: 0 var(--spacing-lg);
            color: var(--text-tertiary);
            font-size: var(--font-size-sm);
        }

        /* === 可访问性增强样式 === */

        /* 屏幕阅读器专用内容 */
        .sr-only {
            position: absolute !important;
            width: 1px !important;
            height: 1px !important;
            padding: 0 !important;
            margin: -1px !important;
            overflow: hidden !important;
            clip: rect(0, 0, 0, 0) !important;
            white-space: nowrap !important;
            border: 0 !important;
        }

        .sr-only-focusable:focus {
            position: static !important;
            width: auto !important;
            height: auto !important;
            padding: inherit !important;
            margin: inherit !important;
            overflow: visible !important;
            clip: auto !important;
            white-space: normal !important;
        }

        /* === 现代化聚焦效果基础样式 === */

        /* 统一聚焦效果 - 优化边角渲染，避免锯齿 */
        .modern-focus,
        input:focus:not(#message-input),
        textarea:focus:not(#message-input),
        select:focus,
        button:focus-visible,
        .btn:focus-visible {
            outline: none !important;
            border-color: var(--focus-border-color) !important;
            border-width: 1.5px !important;
            /* 优化阴影效果，避免边角锯齿 */
            box-shadow:
                0 0 0 2px rgba(100, 181, 246, 0.15),
                0 2px 8px rgba(0, 0, 0, 0.08) !important;
            transition: var(--focus-transition) !important;
            /* 边角渲染优化 */
            border-style: solid !important;
            box-sizing: border-box !important;
            /* 强制硬件加速，优化边角渲染 */
            transform: translateZ(0);
            /* 抗锯齿优化 */
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            /* 边框渲染优化 */
            -webkit-backface-visibility: hidden;
            backface-visibility: hidden;
        }

        /* 移除鼠标点击后的聚焦效果，保持键盘导航可访问性 */
        button:focus:not(:focus-visible),
        .btn:focus:not(:focus-visible) {
            outline: none !important;
            border-color: inherit !important;
            box-shadow: none !important;
        }

        /* 高对比度模式支持 */
        @media (prefers-contrast: high) {
            :root {
                --focus-border-color: #1976d2;
                --focus-glow-color: rgba(25, 118, 210, 0.4);
                --text-primary: #000000;
                --text-secondary: #000000;
                --bg-primary: #ffffff;
                --bg-secondary: #ffffff;
                --border-primary: #000000;
                --border-secondary: #000000;
            }

            .btn {
                border-width: 2px;
            }

            .form-control {
                border-width: 2px;
            }

            .message-bubble {
                border-width: 2px;
            }
        }

        /* 键盘导航增强 */
        .keyboard-navigation {
            outline: none;
        }

        .keyboard-navigation:focus-visible {
            outline: none;
            border-color: var(--focus-border-color);
            box-shadow: 0 0 0 var(--focus-glow-radius) var(--focus-glow-color);
            transition: var(--focus-transition);
        }

        /* 跳转链接 */
        .skip-link {
            position: absolute;
            top: -40px;
            left: 6px;
            background: var(--bg-primary);
            color: var(--text-primary);
            padding: 8px;
            text-decoration: none;
            border-radius: var(--radius-base);
            border: 2px solid var(--focus-border-color);
            box-shadow: 0 0 0 var(--focus-glow-radius) var(--focus-glow-color);
            z-index: var(--z-toast);
            transition: top var(--duration-fast) var(--ease-out);
        }

        .skip-link:focus {
            top: 6px;
        }

        /* 可访问的按钮状态 */
        [aria-pressed="true"] {
            background-color: var(--color-primary);
            color: white;
        }

        [aria-expanded="true"] .fa-chevron-down {
            transform: rotate(180deg);
        }

        /* 可访问的表单标签 */
        .form-label {
            display: block;
            margin-bottom: var(--spacing-xs);
            font-weight: var(--font-weight-medium);
            color: var(--text-primary);
            font-size: var(--font-size-sm);
        }

        .form-label.required::after {
            content: " *";
            color: var(--color-danger);
        }

        /* 错误和帮助文本 */
        .form-text {
            margin-top: var(--spacing-xs);
            font-size: var(--font-size-xs);
            color: var(--text-tertiary);
        }

        .form-error {
            margin-top: var(--spacing-xs);
            font-size: var(--font-size-xs);
            color: var(--color-danger);
        }

        .form-success {
            margin-top: var(--spacing-xs);
            font-size: var(--font-size-xs);
            color: var(--color-success);
        }

        /* 可访问的模态框 */
        .modal-overlay[aria-hidden="true"] {
            display: none;
        }

        .modal-dialog[role="dialog"] {
            position: relative;
        }

        /* 可访问的下拉菜单 */
        [role="menu"] {
            background-color: var(--bg-elevated);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-base);
            box-shadow: var(--shadow-lg);
            padding: var(--spacing-xs);
        }

        [role="menuitem"] {
            display: block;
            width: 100%;
            padding: var(--spacing-sm) var(--spacing-md);
            color: var(--text-primary);
            text-decoration: none;
            border-radius: var(--radius-sm);
            transition: background-color var(--duration-fast) var(--ease-out);
        }

        [role="menuitem"]:hover,
        [role="menuitem"]:focus {
            background-color: var(--color-gray-200);
            outline: none;
        }

        [role="menuitem"][aria-disabled="true"] {
            color: var(--text-muted);
            cursor: not-allowed;
            pointer-events: none;
        }

        /* 可访问的标签页 */
        [role="tablist"] {
            display: flex;
            border-bottom: 1px solid var(--border-secondary);
        }

        [role="tab"] {
            padding: var(--spacing-md) var(--spacing-lg);
            border: none;
            background: none;
            color: var(--text-secondary);
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all var(--duration-fast) var(--ease-out);
        }

        [role="tab"]:hover {
            color: var(--text-primary);
            background-color: var(--color-gray-100);
        }

        [role="tab"][aria-selected="true"] {
            color: var(--color-primary);
            border-bottom-color: var(--color-primary);
        }

        [role="tab"]:focus {
            outline: none;
            border-color: var(--focus-border-color);
            box-shadow: 0 0 0 var(--focus-glow-radius) var(--focus-glow-color);
            transition: var(--focus-transition);
        }

        [role="tabpanel"] {
            padding: var(--spacing-lg);
        }

        [role="tabpanel"][aria-hidden="true"] {
            display: none;
        }

        /* === 顶部栏样式 === */

        #top-bar {
            height: 55px;
            background: var(--bg-primary);
            border-bottom: 1px solid var(--border-primary);
            display: flex;
            align-items: center;
            padding: 0 var(--spacing-md);
            width: 100%;
            z-index: var(--z-sticky);
            flex-shrink: 0;
            position: relative;
            /* 添加平滑过渡效果 */
            transition: background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                       border-color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }



        /* 菜单切换按钮基础布局 */
        #menu-toggle-button {
            width: 34px;
            height: 34px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: flex-start;
            padding-left: var(--spacing-sm);
            margin-right: var(--spacing-sm);
            border-radius: var(--radius-full);
            position: relative;
            z-index: calc(var(--z-dropdown) + 1);
            /* 样式和交互效果已迁移到 design-system.css 统一管理 */
        }

        /* 汉堡菜单线条 */
        #menu-toggle-button span {
            display: block;
            background-color: currentColor;
            height: 2px;
            margin: 3px 0;
            border-radius: var(--radius-sm);
            transition: all var(--duration-base) var(--ease-in-out);
            transform-origin: center;
        }

        #menu-toggle-button span:nth-child(1) { width: 20px; }
        #menu-toggle-button span:nth-child(2) { width: 15px; }
        #menu-toggle-button span:nth-child(3) { width: 10px; }

        /* 汉堡菜单到X的动画 */
        #sidebar.open ~ #app-container #top-bar #menu-toggle-button span:nth-child(1) {
            width: 20px;
            transform: translateY(4.5px) rotate(45deg);
        }

        #sidebar.open ~ #app-container #top-bar #menu-toggle-button span:nth-child(2) {
            opacity: 0;
            width: 16px;
            transform: scaleX(0);
        }

        #sidebar.open ~ #app-container #top-bar #menu-toggle-button span:nth-child(3) {
            width: 20px;
            transform: translateY(-4.5px) rotate(-45deg);
        }

        /* 聊天标题 */
        #chat-title {
            flex-grow: 1;
            text-align: center;
            font-weight: var(--font-weight-medium);
            font-size: var(--font-size-lg);
            color: var(--text-primary);
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            padding: 0 var(--spacing-md);
            line-height: var(--line-height-tight);
        }

        /* 顶部操作区域 */
        #top-actions {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            margin-left: auto;
        }

        /* 顶部操作按钮 - 现代化设计 */
        .top-action-button {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 0.5px solid transparent;
            border-radius: 6px;
            color: var(--text-secondary);
            background-color: transparent;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            /* 统一内边距，确保图标在所有主题下居中对齐 */
            padding: 7.5px !important;
        }

        /* 顶部操作按钮悬停效果已迁移到 design-system.css 统一管理 */

        .top-action-button:focus {
            outline: none;
        }

        .top-action-button:focus-visible {
            outline: none;
            border-color: var(--focus-border-color);
            box-shadow: 0 0 0 var(--focus-glow-radius) var(--focus-glow-color);
            transition: var(--focus-transition);
        }

        .top-action-button:active {
            background-color: var(--color-gray-200);
            border-color: var(--color-gray-400);
            transform: translateY(0);
        }

        /* === 对话选项栏样式 === */

        #conversation-options-bar {
            height: 48px;
            background-color: var(--bg-primary);
            border-bottom: 1px solid var(--border-secondary);
            display: flex;
            align-items: center;
            padding: 0 var(--spacing-lg);
            width: 100%;
            z-index: calc(var(--z-sticky) - 10);
            flex-shrink: 0;
            gap: var(--spacing-md);
            position: relative;
            /* 添加平滑过渡效果 */
            transition: background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                       border-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                       height 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        #conversation-options-bar label {
            font-size: var(--font-size-sm);
            font-weight: var(--font-weight-medium);
            color: var(--text-secondary);
            flex-shrink: 0;
            margin-right: var(--spacing-xs);
            line-height: var(--line-height-tight);
        }

        .conversation-settings-button-wrapper {
            margin-left: var(--spacing-md);
            flex-shrink: 0;
        }

        #conversation-settings-button {
            width: 34px;
            height: 34px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: var(--radius-full);
            color: var(--text-secondary);
            background-color: transparent;
            font-size: var(--font-size-lg);
            transition: all var(--duration-fast) var(--ease-out);
        }

        #conversation-settings-button:hover {
            color: var(--text-primary);
            background-color: var(--color-gray-200);
        }

        #conversation-settings-button:focus-visible {
            outline: none;
            border-color: var(--focus-border-color);
            box-shadow: 0 0 0 var(--focus-glow-radius) var(--focus-glow-color);
            transition: var(--focus-transition);
        }

        #conversation-settings-button:active {
            background-color: var(--color-gray-300);
        }

        /* === 自定义选择器样式 === */

        .custom-selector-container {
            position: relative;
            flex-grow: 1;
            min-width: 150px;
            max-width: 100%;
        }

        /* 选择器触发器 */
        .custom-selector-trigger {
            height: 38px;
            width: 100%;
            padding: var(--spacing-xs) var(--spacing-md);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-base);
            background-color: var(--bg-primary);
            color: var(--text-primary);
            font-size: var(--font-size-sm);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            box-shadow: var(--shadow-inner);
            transition: all var(--duration-fast) var(--ease-out);
        }

        .custom-selector-trigger:hover {
            border-color: var(--color-gray-500);
        }

        .custom-selector-trigger.active,
        .custom-selector-trigger:focus-visible {
            outline: none;
            border-color: var(--focus-border-color);
            box-shadow: 0 0 0 var(--focus-glow-radius) var(--focus-glow-color);
            transition: var(--focus-transition);
        }

        .custom-selector-trigger:disabled {
            background-color: var(--color-gray-100);
            color: var(--text-muted);
            cursor: not-allowed;
            opacity: 0.7;
        }

        /* 选择器显示值 */
        .custom-selector-trigger .selected-value-display {
            flex-grow: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            text-align: left;
            padding-left: var(--spacing-xs);
            font-size: var(--font-size-sm);
        }

        /* 下拉箭头 */
        .custom-selector-trigger .fa-chevron-down {
            font-size: 0.7em;
            color: var(--text-tertiary);
            flex-shrink: 0;
            transition: transform var(--duration-fast) var(--ease-out);
        }

        .custom-selector-trigger.active .fa-chevron-down {
            transform: rotate(180deg);
        }

        /* 选择器面板 */
        .custom-selector-panel {
            position: absolute;
            top: calc(100% + 4px);
            left: 0;
            min-width: 100%;
            max-height: 300px;
            background-color: var(--bg-elevated);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-md);
            box-shadow: var(--shadow-lg);
            z-index: var(--z-dropdown);
            overflow-y: auto;
            padding: var(--spacing-xs);
            display: none;
            opacity: 0;
            transform: translateY(-5px);
            transition: opacity var(--duration-fast) var(--ease-out),
                        transform var(--duration-fast) var(--ease-out);
        }

        .custom-selector-panel.visible {
            display: block;
            opacity: 1;
            transform: translateY(0);
        }

        /* 选择器选项 */
        .custom-selector-option {
            padding: var(--spacing-sm) var(--spacing-md);
            font-size: var(--font-size-sm);
            cursor: pointer;
            border-radius: var(--radius-sm);
            white-space: normal;
            word-wrap: break-word;
            overflow-wrap: break-word;
            line-height: var(--line-height-normal);
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            transition: background-color var(--duration-fast) var(--ease-out);
        }

        .custom-selector-option:hover {
            background-color: var(--color-gray-100);
        }

        .custom-selector-option.selected {
            background-color: var(--color-primary-light);
            color: var(--color-primary);
            font-weight: var(--font-weight-medium);
        }

        .custom-selector-option.selected:hover {
            background-color: var(--color-primary-light);
        }

        .custom-selector-option[data-value="add_custom"] {
            margin-top: var(--spacing-xs);
            padding-top: var(--spacing-sm);
            color: var(--color-primary);
            border-top: 1px solid var(--border-secondary);
        }

        /* 选项组标签 */
        .custom-selector-optgroup-label {
            padding: var(--spacing-sm) var(--spacing-md) var(--spacing-xs) var(--spacing-md);
            font-size: var(--font-size-xs);
            color: var(--text-tertiary);
            font-weight: var(--font-weight-semibold);
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        /* 删除按钮 */
        .custom-model-option-delete {
            margin-left: auto;
            padding: var(--spacing-xs) var(--spacing-xs);
            font-size: var(--font-size-xs);
            color: var(--color-danger);
            background: none;
            border: none;
            cursor: pointer;
            opacity: 0.6;
            flex-shrink: 0;
            border-radius: var(--radius-sm);
            transition: all var(--duration-fast) var(--ease-out);
        }

        .custom-selector-option:hover .custom-model-option-delete {
            opacity: 1;
        }

        .custom-model-option-delete:hover {
            color: var(--color-danger-hover);
            background-color: var(--color-danger-light);
        }

        /* === 侧边栏样式 === */

        #sidebar {
            width: 280px;
            max-width: 85%;
            height: 100vh;
            position: fixed;
            left: 0;
            top: 0;
            z-index: var(--z-sidebar);
            background: var(--bg-primary);
            border-right: 1px solid var(--border-primary);
            display: flex;
            flex-direction: column;
            transform: translateX(-100%);
            transition: transform var(--duration-base) var(--ease-out);
            will-change: transform;
            overflow: hidden;
        }

        /* 侧边栏打开状态 */
        #sidebar.open {
            transform: translateX(0);
        }

        /* 侧边栏内容区域 */
        #sidebar-content {
            display: flex;
            flex-direction: column;
            height: 100%;
            overflow: hidden;
        }



        /* 侧边栏头部 */
        #sidebar-header {
            padding: var(--spacing-lg) var(--spacing-lg) var(--spacing-md) var(--spacing-lg);
            border-bottom: 1px solid var(--border-secondary);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-shrink: 0;
            background: var(--bg-primary);
            min-height: 64px;
            position: relative;
        }

        #sidebar-header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: var(--spacing-lg);
            right: var(--spacing-lg);
            height: 1px;
            background: linear-gradient(90deg,
                        transparent 0%,
                        var(--border-primary) 20%,
                        var(--border-primary) 80%,
                        transparent 100%);
        }

        #sidebar-header h2 {
            margin: 0;
            font-size: var(--font-size-lg);
            font-weight: var(--font-weight-semibold);
            color: var(--text-primary);
            letter-spacing: -0.025em;
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        #sidebar-header h2::before {
            content: "💬";
            font-size: 1.2em;
            filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
        }

        #sidebar-close-button {
            background: none;
            border: none;
            font-size: 1.4em;
            color: var(--text-tertiary);
            padding: var(--spacing-xs);
            border-radius: var(--radius-md);
            transition: all var(--duration-fast) var(--ease-smooth);
            cursor: pointer;
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        /* 侧边栏关闭按钮所有效果已迁移到 design-system.css 统一管理 */

        /* 侧边栏控制区域 */
        #sidebar-controls {
            padding: var(--spacing-md) var(--spacing-lg) var(--spacing-lg) var(--spacing-lg);
            border-bottom: 1px solid var(--border-secondary);
            background: linear-gradient(135deg,
                        rgba(255, 255, 255, 0.7) 0%,
                        rgba(248, 249, 250, 0.5) 100%);
            display: flex;
            flex-direction: column;
            gap: var(--spacing-md);
            flex-shrink: 0;
            position: relative;
        }

        #sidebar-controls::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: var(--spacing-lg);
            right: var(--spacing-lg);
            height: 1px;
            background: linear-gradient(90deg,
                        transparent 0%,
                        var(--border-secondary) 20%,
                        var(--border-secondary) 80%,
                        transparent 100%);
        }

        /* 搜索输入框容器 */
        .search-input-container {
            position: relative;
            width: 100%;
        }

        /* 搜索输入框 */
        #session-search-input {
            width: 100%;
            padding: var(--spacing-sm) var(--spacing-md) var(--spacing-sm) 36px;
            border: 1px solid var(--border-secondary);
            border-radius: var(--radius-lg);
            background: linear-gradient(135deg,
                        rgba(255, 255, 255, 0.9) 0%,
                        rgba(248, 249, 250, 0.8) 100%);
            color: var(--text-primary);
            font-size: var(--font-size-sm);
            transition: all var(--duration-fast) var(--ease-smooth);
            box-sizing: border-box;
            backdrop-filter: blur(5px);
        }

        #session-search-input:focus {
            outline: none;
            border-color: var(--focus-border-color);
            box-shadow: 0 0 0 var(--focus-glow-radius) var(--focus-glow-color);
            background: rgba(255, 255, 255, 0.8);  /* 保持原始背景 */
            transition: var(--focus-transition);
        }

        #session-search-input::placeholder {
            color: var(--text-tertiary);
            font-style: italic;
            padding-left: 4px;
        }

        /* 搜索图标 */
        .search-input-container::before {
            content: "🔍";
            position: absolute;
            left: var(--spacing-sm);
            top: 50%;
            transform: translateY(-50%);
            font-size: 0.9em;
            color: var(--text-tertiary);
            pointer-events: none;
            z-index: 1;
        }

        /* 侧边栏按钮组 */
        .sidebar-io-buttons {
            display: flex;
            gap: var(--spacing-sm);
            flex-wrap: wrap;
        }

        .sidebar-io-button {
            /* 继承统一按钮基类样式 */
            flex: 1;
            min-width: 0;
            padding: var(--spacing-sm) var(--spacing-md);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-lg);
            background: var(--bg-surface);
            color: var(--text-secondary);
            font-size: var(--font-size-sm);
            font-weight: var(--font-weight-medium);
            cursor: pointer;
            transition: all var(--duration-fast) var(--ease-out);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--spacing-xs);
            box-shadow: var(--shadow-xs);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            min-height: 36px;
            line-height: var(--line-height-tight);
        }

        .sidebar-io-button:hover {
            background: var(--bg-hover, var(--color-gray-100));
            border-color: var(--border-secondary);
            color: var(--text-primary);
            box-shadow: var(--shadow-sm);
            transform: translateY(-0.5px);
        }

        .sidebar-io-button:active {
            background: var(--bg-active, var(--color-gray-200));
            border-color: var(--border-tertiary);
            transform: translateY(0);
            box-shadow: var(--shadow-xs);
        }

        .sidebar-io-button:focus {
            outline: none;
        }

        .sidebar-io-button:focus-visible {
            outline: none; /* 移除outline避免在overflow容器中被裁剪 */
            /* 使用box-shadow模拟outline效果，不会被overflow裁剪 */
            box-shadow:
                0 0 0 2px var(--focus-border-color), /* 模拟outline */
                0 0 0 4px var(--focus-glow-color); /* 模拟outline-offset + glow */
            transition: var(--focus-transition);
        }

        .sidebar-io-button i {
            font-size: 12px;
            flex-shrink: 0;
        }

        /* 查看后端日志按钮特殊样式 */
        #view-backend-log-button {
            width: 100%;
            margin-top: var(--spacing-sm);
            background: linear-gradient(135deg,
                        rgba(245, 158, 11, 0.1) 0%,
                        rgba(217, 119, 6, 0.05) 100%);
            border-color: rgba(245, 158, 11, 0.3);
            color: var(--color-warning);
        }

        #view-backend-log-button:hover {
            background: linear-gradient(135deg,
                        rgba(245, 158, 11, 0.15) 0%,
                        rgba(217, 119, 6, 0.1) 100%);
            border-color: var(--color-warning);
            color: var(--color-warning-hover);
        }





        /* === 会话列表现代化滚动条 === */
        #session-list {
            flex-grow: 1;
            overflow-y: auto;
            padding: var(--spacing-sm) 0; /* 恢复原始padding，问题已通过移除border-bottom: none解决 */
            scrollbar-width: thin;
            scrollbar-color: rgba(0, 0, 0, 0.15) transparent;
            position: relative;
        }

        /* 侧边栏会话列表滚动条隐藏 - 已通过全局设置处理 */

        /* 代理列表 */
        #agent-list {
            border-top: 1px solid var(--border-primary);
            padding-top: var(--spacing-xs);
            margin-top: var(--spacing-xs);
            flex-shrink: 0;
        }

        #agent-list-header {
            padding: var(--spacing-sm) var(--spacing-lg);
            font-weight: var(--font-weight-medium);
            font-size: var(--font-size-sm);
            color: var(--text-secondary);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        #agent-list-header h3 {
            margin: 0;
            font-size: var(--font-size-sm);
            font-weight: var(--font-weight-medium);
            color: var(--text-secondary);
        }

        /* 智能体创建按钮 - 统一到按钮组件系统标准 */
        #create-agent-button {
            /* 继承统一按钮基类样式 */
            display: flex;
            align-items: center;
            justify-content: center;
            padding: var(--spacing-xs) var(--spacing-sm); /* 调整内边距以适应图标按钮 */
            min-height: 32px; /* 适中的高度 */

            /* 统一的边框和圆角 */
            border: 1px solid transparent;
            border-radius: var(--radius-lg); /* 使用统一圆角 */

            /* 统一的背景和颜色 */
            background: transparent;
            color: var(--color-primary);

            /* 统一的字体样式 */
            font-size: var(--font-size-lg);
            font-weight: var(--font-weight-medium);

            /* 统一的交互样式 */
            cursor: pointer;
            user-select: none;
            transition: all var(--duration-fast) var(--ease-out);

            /* 统一的阴影效果 */
            box-shadow: none; /* 图标按钮不需要阴影 */
        }

        #create-agent-button:hover {
            background: var(--bg-hover, var(--color-primary-light));
            border-color: var(--border-secondary);
            color: var(--color-primary-hover);
            transform: translateY(-0.5px); /* 统一的悬停位移效果 */
        }

        #create-agent-button:active {
            background: var(--bg-active, var(--color-primary-light));
            border-color: var(--border-tertiary);
            transform: translateY(0) scale(0.95); /* 统一的激活效果 */
            transition-duration: 0.1s;
        }

        #create-agent-button:focus {
            outline: none;
        }

        #create-agent-button:focus-visible {
            outline: none; /* 移除outline避免在overflow容器中被裁剪 */
            /* 使用box-shadow模拟outline效果，不会被overflow裁剪 */
            box-shadow:
                0 0 0 2px var(--focus-border-color), /* 模拟outline */
                0 0 0 4px var(--focus-glow-color); /* 模拟outline-offset + glow */
            transition: var(--focus-transition);
        }

        #agent-list-items {
            max-height: 150px;
            overflow-y: auto;
            padding: var(--spacing-sm) 0; /* 使用box-shadow后不需要额外内边距 */
        }

        /* 会话项样式 - 统一到按钮组件系统标准，优化内部布局 */
        .session-item {
            /* 继承统一按钮基类样式 */
            display: flex;
            align-items: center;
            padding: var(--spacing-sm) var(--spacing-lg);
            padding-right: 48px; /* 为删除按钮预留足够空间 */
            margin: var(--spacing-xs) var(--spacing-sm);
            min-height: 40px; /* 增加高度以改善触摸体验 */

            /* 统一的边框和圆角 */
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-lg);

            /* 统一的背景和颜色 - 采用暗黑主题的渐变设计理念 */
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.9) 0%,
                rgba(248, 250, 252, 0.95) 100%);
            color: var(--text-secondary);

            /* 统一的字体样式 */
            font-size: var(--font-size-sm);
            font-weight: var(--font-weight-medium);
            line-height: var(--line-height-tight);

            /* 统一的交互样式 */
            cursor: pointer;
            user-select: none;
            transition: all var(--duration-fast) var(--ease-out);
            box-sizing: border-box;

            /* 统一的阴影效果 - 采用暗黑主题的层次感设计 */
            box-shadow: var(--shadow-sm);

            /* 对话列表特有样式 */
            position: relative;
            white-space: nowrap;

            /* 确保内容垂直居中 */
            justify-content: flex-start;
        }

        .session-item + .session-item {
            margin-top: var(--spacing-xs);
        }

        /* 智能体项特殊样式 - 采用暗黑主题的强调色设计 */
        .session-item.agent-item {
            background: linear-gradient(135deg,
                rgba(139, 92, 246, 0.08) 0%,
                rgba(124, 58, 237, 0.05) 100%);
            border-left: 3px solid var(--accent-purple, var(--color-primary));
            padding-left: calc(var(--spacing-lg) - 3px);
            /* 继承统一的圆角和其他样式 */
        }

        .session-item.agent-item::before {
            content: "🤖";
            margin-right: var(--spacing-xs);
            font-size: 1.1em;
            filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
        }

        /* 智能体项悬停状态 - 保持紫色主题 */
        .session-item.agent-item:hover {
            background: linear-gradient(135deg,
                rgba(139, 92, 246, 0.12) 0%,
                rgba(124, 58, 237, 0.08) 100%);
            border-left-color: var(--accent-purple, var(--color-primary));
        }

        /* 智能体项选中状态 - 紫色渐变设计 */
        .session-item.agent-item.active {
            background: linear-gradient(135deg,
                rgba(139, 92, 246, 0.2) 0%,
                rgba(124, 58, 237, 0.15) 100%);
            border-left-color: var(--accent-purple, var(--color-primary));
            color: var(--accent-purple, var(--color-primary));
        }

        /* 智能体项选中悬停状态 */
        .session-item.agent-item.active:hover {
            background: linear-gradient(135deg,
                rgba(139, 92, 246, 0.25) 0%,
                rgba(124, 58, 237, 0.2) 100%);
        }

        .session-item span {
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            line-height: var(--line-height-tight);
            margin-right: var(--spacing-sm); /* 减少右边距，为删除按钮留出合适空间 */
            min-width: 0; /* 确保文本可以正确收缩 */
            display: flex;
            align-items: center;
        }

        /* 移除最后一个元素的底部边框移除规则，确保选中状态边框完整显示 */
        /* 注释掉以下规则，让选中状态的蓝色边框能够完整显示 */
        /*
        #session-list .session-item:last-child,
        #agent-list-items .session-item:last-child {
            border-bottom: none;
        }
        */

        /* 统一的悬停效果 - 采用暗黑主题的渐变设计理念 */
        .session-item:hover {
            background: linear-gradient(135deg,
                rgba(241, 245, 249, 0.9) 0%,
                rgba(226, 232, 240, 0.95) 100%);
            border-color: var(--border-focus, var(--border-secondary));
            color: var(--text-primary);
            box-shadow: var(--shadow-md);
            transform: translateY(-0.5px);
        }

        /* 统一的激活状态 - 采用暗黑主题的蓝色渐变设计 */
        .session-item.active {
            background: linear-gradient(135deg,
                rgba(59, 130, 246, 0.15) 0%,
                rgba(37, 99, 235, 0.1) 100%);
            color: var(--color-primary);
            font-weight: var(--font-weight-medium);
            border-color: var(--color-primary);
            box-shadow: var(--shadow-md);
        }

        .session-item.active:hover {
            background: linear-gradient(135deg,
                rgba(59, 130, 246, 0.2) 0%,
                rgba(37, 99, 235, 0.15) 100%);
            border-color: var(--color-primary-hover);
            box-shadow: var(--shadow-lg);
            transform: translateY(-0.5px);
        }

        /* 统一的点击反馈 - 采用暗黑主题的深度渐变设计 */
        .session-item:active {
            background: linear-gradient(135deg,
                rgba(203, 213, 225, 0.95) 0%,
                rgba(186, 200, 216, 0.98) 100%);
            border-color: var(--border-tertiary);
            transform: translateY(0);
            box-shadow: var(--shadow-xs);
            transition-duration: 0.1s;
        }

        /* 统一的聚焦状态 */
        .session-item:focus {
            outline: none;
        }

        .session-item:focus-visible {
            outline: none; /* 移除outline避免在overflow容器中被裁剪 */
            /* 使用box-shadow模拟outline效果，不会被overflow裁剪 */
            box-shadow:
                0 0 0 2px var(--focus-border-color), /* 模拟outline */
                0 0 0 4px var(--focus-glow-color); /* 模拟outline-offset + glow */
            transition: var(--focus-transition);
        }



        /* 会话项加载状态 */
        .session-item.loading {
            opacity: 0.7;
            pointer-events: none;
        }

        .session-item.loading::after {
            content: "";
            position: absolute;
            right: 60px;
            top: 50%;
            transform: translateY(-50%);
            width: 12px;
            height: 12px;
            border: 2px solid var(--color-primary);
            border-top: 2px solid transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: translateY(-50%) rotate(0deg); }
            100% { transform: translateY(-50%) rotate(360deg); }
        }

        /* 新创建会话的高亮动画 */
        .session-item.newly-created {
            animation: highlightNew 1.5s ease-out;
        }

        @keyframes highlightNew {
            0% { background-color: var(--color-primary-light); }
            100% { background-color: transparent; }
        }

        .session-item.active.newly-created {
            animation: highlightNewActive 1.5s ease-out;
        }

        @keyframes highlightNewActive {
            0% { background-color: var(--color-primary); }
            100% { background-color: var(--color-primary-light); }
        }

        /* === 微交互动画增强 === */
        @keyframes subtleFloat {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-1px); }
        }



        @keyframes gentlePulse {
            0%, 100% {
                box-shadow:
                    0 2px 8px rgba(0, 0, 0, 0.1),
                    inset 0 1px 0 rgba(255, 255, 255, 0.8);
            }
            50% {
                box-shadow:
                    0 4px 16px rgba(0, 0, 0, 0.15),
                    inset 0 1px 0 rgba(255, 255, 255, 0.9);
            }
        }

        @keyframes lightGlow {
            0%, 100% {
                box-shadow:
                    0 2px 8px rgba(37, 99, 235, 0.1),
                    inset 0 1px 0 rgba(255, 255, 255, 0.8);
            }
            50% {
                box-shadow:
                    0 4px 16px rgba(37, 99, 235, 0.2),
                    0 0 24px rgba(37, 99, 235, 0.1),
                    inset 0 1px 0 rgba(255, 255, 255, 0.9);
            }
        }

        /* 会话删除按钮 - 使用高优先级选择器确保精确控制 */
        .session-item .session-delete-button {
            position: absolute;
            right: 8px; /* 使用绝对像素值确保精确控制 */
            top: 50%;
            transform: translateY(-50%);
            background: transparent; /* 默认透明背景 */
            border: none;
            color: var(--text-tertiary);
            cursor: pointer;
            padding: 2px; /* 使用绝对像素值确保精确控制 */
            border-radius: 2px; /* 使用绝对像素值确保精确控制 */
            opacity: 0;
            transition: all var(--duration-fast) var(--ease-out);
            font-size: var(--font-size-xs); /* 使用更小的字体 */
            width: 20px; /* 使用绝对像素值确保精确控制 */
            height: 20px; /* 使用绝对像素值确保精确控制 */
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 2;
            box-sizing: border-box;
            min-width: 20px;
            max-width: 20px;
            min-height: 20px;
            max-height: 20px;
        }

        .session-item:hover .session-delete-button {
            opacity: 1;
        }

        /* 删除按钮悬停效果已迁移到 design-system.css 统一管理 */

        .session-item.active .session-delete-button {
            color: var(--color-primary);
            opacity: 0.7;
        }

        /* 智能体项选中状态下的删除按钮 */
        .session-item.agent-item.active .session-delete-button {
            color: var(--accent-purple, var(--color-primary));
            opacity: 0.7;
        }

        /* 智能体项删除按钮悬停效果 - 与普通删除按钮保持一致 */
        .session-item.agent-item .session-delete-button:hover {
            background: rgba(239, 68, 68, 0.1); /* 使用红色系背景，更加精细 */
            color: var(--color-danger);
            opacity: 1;
            border-radius: var(--radius-xs); /* 使用最小圆角（2px），确保背景块精细化 */
            transform: translateY(-50%); /* 移除缩放动效，只保持垂直居中 */
            right: var(--spacing-sm); /* 明确使用8px定位，与基础样式保持一致 */
        }

        /* 智能体项选中状态删除按钮悬停效果 */
        .session-item.agent-item.active .session-delete-button:hover {
            background: rgba(239, 68, 68, 0.1); /* 使用红色系背景，更加精细 */
            color: var(--color-danger);
            opacity: 1;
            border-radius: var(--radius-xs); /* 使用最小圆角（2px），确保背景块精细化 */
            transform: translateY(-50%); /* 移除缩放动效，只保持垂直居中 */
            right: var(--spacing-sm); /* 明确使用8px定位，与基础样式保持一致 */
        }

        /* 普通对话记录选中状态删除按钮悬停效果 - 与其他状态保持一致 */
        .session-item.active .session-delete-button:hover {
            background: rgba(239, 68, 68, 0.1); /* 使用红色系背景，更加精细 */
            color: var(--color-danger);
            opacity: 1;
            border-radius: var(--radius-xs); /* 使用最小圆角（2px），确保背景块精细化 */
            transform: translateY(-50%); /* 移除缩放动效，只保持垂直居中 */
            right: var(--spacing-sm); /* 明确使用8px定位，与基础样式保持一致 */
        }

        .session-delete-button:focus {
            outline: none !important;
            box-shadow: none !important;
        }

        /* 删除按钮专用聚焦样式 - 保持位置稳定 */
        .session-delete-button:focus-visible {
            outline: none !important;
            border-color: var(--focus-border-color) !important;
            box-shadow: 0 0 0 var(--focus-glow-radius) var(--focus-glow-color) !important;
            transform: translateY(-50%) !important; /* 保持垂直居中，移除多余动画 */
            transition: var(--focus-transition) !important;
        }

        .session-delete-button:hover {
            background: rgba(239, 68, 68, 0.1); /* 使用红色系背景，更加精细 */
            color: var(--color-danger);
            opacity: 1;
            border-radius: var(--radius-xs); /* 使用最小圆角（2px），确保背景块精细化 */
            transform: translateY(-50%); /* 移除缩放动效，只保持垂直居中 */
            /* 确保定位与基础样式一致，避免向右偏移 */
            right: var(--spacing-sm); /* 明确使用8px定位，与基础样式保持一致 */
        }

        .session-delete-button:active {
            transform: translateY(-50%); /* 移除缩放动效，只保持垂直居中 */
            background: rgba(239, 68, 68, 0.2); /* 红色系激活背景 */
            border-radius: var(--radius-xs); /* 使用最小圆角（2px），确保背景块精细化 */
            /* 确保定位与基础样式一致，避免向右偏移 */
            right: var(--spacing-sm); /* 明确使用8px定位，与基础样式保持一致 */
        }

        /* 侧边栏遮罩层 */
        #sidebar-overlay {
            position: fixed;
            left: 0;
            top: 0;
            width: 100%;
            height: 100vh;
            background: linear-gradient(135deg,
                        rgba(0, 0, 0, 0.4) 0%,
                        rgba(0, 0, 0, 0.2) 100%);
            z-index: calc(var(--z-sidebar) - 1);
            display: none;
            opacity: 0;
            transition: opacity var(--duration-base) var(--ease-smooth);
            backdrop-filter: blur(6px);
            -webkit-backdrop-filter: blur(6px);
        }

        #sidebar-overlay.visible {
            display: block;
            opacity: 1;
        }

        /* === 去除按键点击时的蓝色聚焦框 === */

        /* 去除所有按钮点击时的蓝色聚焦框 */
        button:focus,
        input[type="button"]:focus,
        input[type="submit"]:focus,
        input[type="reset"]:focus,
        .button:focus,
        .btn:focus {
            outline: none !important;
            box-shadow: none !important;
        }

        /* 特定按钮聚焦框修复 - 鼠标点击时隐藏，键盘导航时显示 */
        .top-action-button:focus:not(:focus-visible),
        #menu-toggle-button:focus:not(:focus-visible),
        #conversation-settings-button:focus:not(:focus-visible),
        .sidebar-io-button:focus:not(:focus-visible),
        #create-agent-button:focus:not(:focus-visible),
        #sidebar-close-button:focus:not(:focus-visible),
        .session-delete-button:focus:not(:focus-visible),
        .scroll-button:focus:not(:focus-visible),
        .input-action-button:focus:not(:focus-visible),
        #send-button:focus:not(:focus-visible),
        .message-action-button:focus:not(:focus-visible),
        .suggestion-button:focus:not(:focus-visible),
        .thinking-process-toggle:focus:not(:focus-visible),
        .modal-close-button:focus:not(:focus-visible),
        #save-changes-button:focus:not(:focus-visible),
        #delete-profile-button:focus:not(:focus-visible),
        #set-default-button:focus:not(:focus-visible),
        .code-action-button:focus:not(:focus-visible),
        .code-footer-button:focus:not(:focus-visible),
        .collapse-button:focus:not(:focus-visible),
        #image-gen-submit-button:focus:not(:focus-visible),
        #image-gen-batch-button:focus:not(:focus-visible),
        #image-gen-close-button:focus:not(:focus-visible),
        #enhance-prompt-button:focus:not(:focus-visible),
        #undo-enhance-prompt-button:focus:not(:focus-visible),
        #clear-prompt-button:focus:not(:focus-visible),
        #image-gen-random-seed-button:focus:not(:focus-visible),
        .image-viewer-action-button:focus:not(:focus-visible),
        .image-viewer-nav-button:focus:not(:focus-visible),
        #audio-record-button:focus:not(:focus-visible),
        .tts-play-button:focus:not(:focus-visible),
        #custom-confirm-yes:focus:not(:focus-visible),
        #custom-confirm-no:focus:not(:focus-visible),
        #custom-prompt-ok:focus:not(:focus-visible),
        #custom-prompt-cancel:focus:not(:focus-visible),
        .modal-close:focus:not(:focus-visible),
        .modal-close-button:focus:not(:focus-visible) {
            outline: none !important;
            box-shadow: none !important;
            border: inherit !important;
        }

        /* 键盘导航时保持可访问性 - 现代化聚焦效果 */
        .top-action-button:focus-visible,
        #menu-toggle-button:focus-visible,
        #conversation-settings-button:focus-visible,
        .sidebar-io-button:focus-visible,
        #create-agent-button:focus-visible,
        #sidebar-close-button:focus-visible,
        .scroll-button:focus-visible,
        .input-action-button:focus-visible,
        #send-button:focus-visible,
        .message-action-button:focus-visible,
        .suggestion-button:focus-visible,
        .thinking-process-toggle:focus-visible,
        .modal-close-button:focus-visible,
        #save-changes-button:focus-visible,
        #delete-profile-button:focus-visible,
        #set-default-button:focus-visible,
        .code-action-button:focus-visible,
        .code-footer-button:focus-visible,
        .collapse-button:focus-visible,
        #image-gen-submit-button:focus-visible,
        #image-gen-batch-button:focus-visible,
        #image-gen-close-button:focus-visible,
        #enhance-prompt-button:focus-visible,
        #undo-enhance-prompt-button:focus-visible,
        #clear-prompt-button:focus-visible,
        #image-gen-random-seed-button:focus-visible,
        .image-viewer-action-button:focus-visible,
        .image-viewer-nav-button:focus-visible,
        #audio-record-button:focus-visible,
        .tts-play-button:focus-visible,
        #custom-confirm-yes:focus-visible,
        #custom-confirm-no:focus-visible,
        #custom-prompt-ok:focus-visible,
        #custom-prompt-cancel:focus-visible,
        .modal-close:focus-visible,
        .modal-close-button:focus-visible {
            outline: none !important;
            border-color: var(--focus-border-color) !important;
            box-shadow: 0 0 0 var(--focus-glow-radius) var(--focus-glow-color) !important;
            transition: var(--focus-transition) !important;
        }

        /* 侧边栏响应式设计 */
        @media (max-width: 768px) {
            #sidebar {
                width: 85%;
                max-width: 320px;
            }

            #sidebar-header {
                padding: var(--spacing-sm) var(--spacing-md);
                min-height: 50px;
            }

            #sidebar-header h2 {
                font-size: var(--font-size-base);
            }

            #sidebar-controls {
                padding: var(--spacing-sm) var(--spacing-md);
                gap: var(--spacing-xs);
            }

            .sidebar-io-buttons {
                flex-direction: column;
                gap: var(--spacing-xs);
            }

            .sidebar-io-button {
                flex: none;
                width: 100%;
                padding: var(--spacing-sm);
                font-size: var(--font-size-xs);
            }

            .session-item {
                padding: var(--spacing-sm) var(--spacing-md);
                padding-right: 44px; /* 为删除按钮预留足够空间 */
                min-height: 44px; /* 保持触摸友好的高度 */
                font-size: var(--font-size-xs);
            }

            .session-delete-button {
                width: 18px; /* 在小屏幕上进一步减小尺寸 */
                height: 18px;
                right: var(--spacing-xs); /* 调整位置以适应较小的内边距 */
                font-size: 10px; /* 更小的字体 */
            }

            #agent-list-header {
                padding: var(--spacing-xs) var(--spacing-md);
            }

            #agent-list-items {
                max-height: 120px;
                padding: var(--spacing-xs) 0 var(--spacing-sm) 0; /* 移动端底部仍需足够空间 */
            }
        }

        @media (max-width: 480px) {
            #sidebar {
                width: 90%;
                max-width: 340px;
                /* 为顶部栏保留空间，避免完全覆盖 */
                top: 60px;
                height: calc(100vh - 60px);
                /* 添加圆角和阴影，提升视觉层次 */
                border-radius: 0 var(--radius-lg) var(--radius-lg) 0;
                box-shadow: var(--shadow-xl);
            }

            /* 确保顶部栏在移动端始终可见 */
            #top-bar {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                z-index: calc(var(--z-dropdown) + 2);
                background: var(--bg-primary);
                border-bottom: 1px solid var(--border-primary);
                backdrop-filter: blur(10px);
            }

            /* 主内容区域适配 */
            #app-container {
                padding-top: 60px;
            }

            .sidebar-io-button {
                padding: var(--spacing-xs) var(--spacing-sm);
            }

            .session-item {
                padding: var(--spacing-xs) var(--spacing-sm);
                padding-right: 40px; /* 为删除按钮预留足够空间 */
                min-height: 40px; /* 保持最小触摸友好高度 */
            }

            .session-delete-button {
                width: 16px; /* 在最小屏幕上进一步减小尺寸 */
                height: 16px;
                right: calc(var(--spacing-xs) / 2); /* 更紧凑的位置 */
                font-size: 9px; /* 最小字体 */
            }

            /* 侧边栏打开时的遮罩层 */
            #sidebar.open::before {
                content: '';
                position: fixed;
                top: 60px;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.3);
                z-index: -1;
                backdrop-filter: blur(2px);
            }
        }

        /* === 主内容区域样式 === */

        #main-content {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            width: 100%;
            position: relative;
        }

        #chatbox {
            flex-grow: 1;
            overflow-y: auto;
            padding: var(--spacing-xl);
            padding-bottom: var(--spacing-md);
            display: flex;
            flex-direction: column;
            gap: var(--spacing-xs);
            scroll-behavior: smooth;
            overscroll-behavior-y: contain;
            position: relative;
        }

        /* 主聊天区域滚动条样式已在顶部定义 */

        /* === 滚动按钮样式 === */

        .scroll-buttons-container {
            position: fixed;
            bottom: 130px;
            right: var(--spacing-lg);
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
            z-index: calc(var(--z-fixed) - 10);
            opacity: 0.3;
            visibility: visible;
            /* 添加位置变化的smooth transition */
            transition: opacity var(--duration-base) var(--ease-out),
                        bottom var(--duration-medium) var(--ease-natural);
        }

        .scroll-buttons-container:hover,
        .scroll-buttons-container.active {
            opacity: 1;
        }

        .scroll-button {
            width: 38px;
            height: 38px;
            background-color: rgba(255, 255, 255, 0.95);
            border: 1px solid var(--border-primary);
            color: var(--text-secondary);
            border-radius: var(--radius-full);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-lg);
            box-shadow: var(--shadow-md);
            transition: all var(--duration-fast) var(--ease-out);
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        .scroll-button:hover {
            background-color: var(--color-gray-100);
            color: var(--text-primary);
            box-shadow: var(--shadow-lg);
            transform: translateY(-2px);
        }

        .scroll-button:focus {
            outline: none !important;
            box-shadow: none !important;
        }

        .scroll-button:active {
            background-color: var(--color-gray-200);
            transform: translateY(0) scale(0.95);
            box-shadow: var(--shadow-sm);
        }

        .scroll-button i {
            font-size: 1em;
            transition: transform var(--duration-fast) var(--ease-out);
            position: relative;
            z-index: 1;
        }

        .scroll-button:hover i {
            transform: translateY(-1px);
        }

        .scroll-button::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.05);
            opacity: 0;
            transition: opacity var(--duration-fast) var(--ease-out);
        }

        .scroll-button:hover::before {
            opacity: 1;
        }

        /* === 消息搜索容器样式 === */

        #message-search-container {
            position: absolute;
            top: var(--spacing-md);
            right: var(--spacing-lg);
            z-index: calc(var(--z-fixed) - 20);
            background-color: rgba(255, 255, 255, 0.95);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-2xl);
            padding: var(--spacing-xs) var(--spacing-md);
            box-shadow: var(--shadow-md);
            backdrop-filter: blur(10px);
            display: none;
            align-items: center;
            gap: var(--spacing-xs);
            opacity: 0;
            transform: translateY(-10px);
            transition: opacity var(--duration-base) var(--ease-out),
                        transform var(--duration-base) var(--ease-out);
        }

        #message-search-container.visible {
            display: flex;
            opacity: 1;
            transform: translateY(0);
        }

        #message-search-input {
            border: none;
            outline: none;
            background: transparent;
            font-size: var(--font-size-sm);
            width: 150px;
            padding: var(--spacing-xs);
            color: var(--text-primary);
        }

        #message-search-input::placeholder {
            color: var(--text-muted);
            font-style: italic;
        }

        #message-search-prev,
        #message-search-next,
        #message-search-close {
            background: none;
            border: none;
            cursor: pointer;
            color: var(--text-secondary);
            font-size: var(--font-size-sm);
            padding: var(--spacing-xs) var(--spacing-xs);
            border-radius: var(--radius-sm);
            transition: all var(--duration-fast) var(--ease-out);
        }

        #message-search-prev:hover,
        #message-search-next:hover,
        #message-search-close:hover {
            color: var(--text-primary);
            background-color: var(--color-gray-200);
        }

        #message-search-prev:focus,
        #message-search-next:focus,
        #message-search-close:focus {
            outline: none !important;
            box-shadow: none !important;
        }

        #message-search-prev:active,
        #message-search-next:active,
        #message-search-close:active {
            background-color: var(--color-gray-300);
            transform: scale(0.95);
        }

        /* === 输入区域样式 - 现代极简设计 === */

        #input-container {
            /* 优化输入容器配色 - 创建自然层次感 */
            background: linear-gradient(135deg,
                rgba(248, 250, 252, 0.98) 0%,
                rgba(241, 245, 249, 0.96) 100%);
            backdrop-filter: var(--blur-md);
            box-shadow:
                0 -4px 16px rgba(0, 0, 0, 0.06),
                0 -2px 8px rgba(0, 0, 0, 0.04);
            padding: var(--spacing-lg);
            width: 100%;
            z-index: var(--z-elevated);
            position: relative;
            flex-shrink: 0;
            transition: backdrop-filter var(--duration-fast) var(--ease-out);
        }

        /* 移除复杂的分界线伪元素 - 已清理 */

        /* 输入控制包装器 */
        #input-controls-wrapper {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-sm);
        }

        /* 移除旧的输入操作栏样式，将在新的工具栏中重新定义 */

        .input-action-button:focus-visible {
            outline: none;
            border-color: var(--focus-border-color);
            box-shadow: 0 0 0 var(--focus-glow-radius) var(--focus-glow-color);
            transition: var(--focus-transition);
        }

        .input-action-button:active {
            background-color: var(--color-gray-300);
            transform: scale(0.95);
        }

        /* 移除旧的开关样式，已迁移到新的工具栏按钮 */

        /* TTS 开关样式 */
        .tts-switch {
            position: relative;
            display: inline-block;
            width: 34px;
            height: 20px;
        }

        .tts-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .tts-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: var(--color-gray-400);
            border-radius: var(--radius-2xl);
            transition: all var(--duration-slow) var(--ease-out);
        }

        .tts-slider:before {
            position: absolute;
            content: "";
            height: 14px;
            width: 14px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            border-radius: var(--radius-full);
            transition: all var(--duration-slow) var(--ease-out);
        }

        .tts-switch input:checked + .tts-slider {
            background-color: var(--color-primary);
        }

        .tts-switch input:focus + .tts-slider {
            box-shadow: 0 0 0 2px rgba(168, 197, 240, 0.2);
        }

        .tts-switch input:checked + .tts-slider:before {
            transform: translateX(14px);
        }

        /* 通用开关样式 - 基于TTS开关 */
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 34px;
            height: 20px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: var(--color-gray-400);
            border-radius: var(--radius-2xl);
            transition: all var(--duration-slow) var(--ease-out);
        }

        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 14px;
            width: 14px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            border-radius: var(--radius-full);
            transition: all var(--duration-slow) var(--ease-out);
        }

        .toggle-switch input:checked + .toggle-slider {
            background-color: var(--color-success);
        }

        .toggle-switch input:focus + .toggle-slider {
            box-shadow: 0 0 0 2px rgba(168, 197, 240, 0.2);
        }

        .toggle-switch input:checked + .toggle-slider:before {
            transform: translateX(14px);
        }

        /* === 功能面板样式 === */
        .function-panel {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: var(--bg-overlay);
            backdrop-filter: blur(4px);
            z-index: var(--z-modal);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: var(--spacing-lg);
            opacity: 0;
            visibility: hidden;
            transition: all var(--duration-base) var(--ease-out);
        }

        .function-panel.visible {
            opacity: 1;
            visibility: visible;
        }

        .function-panel-content {
            background-color: var(--bg-elevated);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-2xl);
            max-width: 500px;
            width: 100%;
            max-height: 80vh;
            display: flex;
            flex-direction: column;
            transform: scale(0.95) translateY(20px);
            transition: transform var(--duration-base) var(--ease-out);
        }

        .function-panel.visible .function-panel-content {
            transform: scale(1) translateY(0);
        }

        .function-panel-header {
            padding: var(--spacing-xl) var(--spacing-xl) var(--spacing-lg) var(--spacing-xl);
            border-bottom: 1px solid var(--border-secondary);
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex-shrink: 0;
        }

        .function-panel-header h3 {
            margin: 0;
            font-size: var(--font-size-xl);
            font-weight: var(--font-weight-semibold);
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .panel-close-button {
            background: none;
            border: none;
            color: var(--text-tertiary);
            font-size: var(--font-size-lg);
            cursor: pointer;
            padding: var(--spacing-xs);
            border-radius: var(--radius-sm);
            transition: all var(--duration-fast) var(--ease-out);
            display: flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
        }

        .panel-close-button:hover {
            background-color: var(--color-gray-200);
            color: var(--text-primary);
        }

        .function-panel-body {
            padding: var(--spacing-xl);
            flex-grow: 1;
            overflow-y: auto;
        }

        /* === 语音功能面板样式 === */
        .voice-function-section {
            margin-bottom: var(--spacing-xl);
            padding: var(--spacing-lg);
            border: 1px solid var(--border-secondary);
            border-radius: var(--radius-md);
            background-color: var(--bg-secondary);
        }

        .voice-function-section h4 {
            margin: 0 0 var(--spacing-md) 0;
            font-size: var(--font-size-lg);
            font-weight: var(--font-weight-medium);
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .voice-controls {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-md);
        }

        .voice-controls .btn {
            align-self: flex-start;
        }

        .recording-status {
            padding: var(--spacing-sm);
            border-radius: var(--radius-sm);
            font-size: var(--font-size-sm);
            font-weight: var(--font-weight-medium);
        }

        .recording-status.recording {
            background-color: var(--color-danger-light);
            color: var(--color-danger);
            border: 1px solid var(--color-danger);
        }

        .recording-status.processing {
            background-color: var(--color-warning-light);
            color: var(--color-warning);
            border: 1px solid var(--color-warning);
        }

        .recording-status.success {
            background-color: var(--color-success-light);
            color: var(--color-success);
            border: 1px solid var(--color-success);
        }

        .recording-status.warning {
            background-color: var(--color-warning-light);
            color: var(--color-warning);
            border: 1px solid var(--color-warning);
        }

        .stt-result {
            margin-top: var(--spacing-md);
            padding: var(--spacing-md);
            background-color: var(--bg-primary);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-sm);
            min-height: 60px;
            font-family: var(--font-family-base);
            line-height: var(--line-height-normal);
            position: relative;
        }

        .stt-result .interim-result {
            color: var(--text-tertiary);
            font-style: italic;
        }

        .stt-result .placeholder {
            color: var(--text-tertiary);
            font-style: italic;
        }

        .stt-result-actions {
            margin-top: var(--spacing-md);
            display: flex;
            gap: var(--spacing-sm);
            justify-content: flex-end;
        }

        .stt-result-actions .btn {
            font-size: var(--font-size-sm);
            padding: var(--spacing-xs) var(--spacing-sm);
        }

        /* 语音支持信息样式 */
        .voice-support-info {
            padding: var(--spacing-md);
            border-radius: var(--radius-sm);
            margin-bottom: var(--spacing-md);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            font-size: var(--font-size-sm);
        }

        .voice-support-info.warning {
            background-color: var(--color-warning-light);
            border: 1px solid var(--color-warning);
            color: var(--color-warning-dark);
        }

        .voice-support-info.info {
            background-color: var(--color-primary-light);
            border: 1px solid var(--color-primary-200);
            color: var(--color-primary-dark);
        }

        .voice-support-info i {
            font-size: var(--font-size-base);
            flex-shrink: 0;
        }

        .voice-selector-container {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-xs);
        }

        .voice-selector-container label {
            font-size: var(--font-size-sm);
            font-weight: var(--font-weight-medium);
            color: var(--text-secondary);
        }

        .tts-controls {
            display: flex;
            gap: var(--spacing-sm);
            flex-wrap: wrap;
        }

        /* === AI工具集面板样式 === */
        .tools-description {
            margin-bottom: var(--spacing-lg);
            padding: var(--spacing-md);
            background-color: var(--color-primary-light);
            border: 1px solid var(--color-primary-200);
            border-radius: var(--radius-sm);
            color: var(--text-secondary);
            font-size: var(--font-size-sm);
        }

        .ai-tools-list {
            margin-bottom: var(--spacing-lg);
            max-height: 300px;
            overflow-y: auto;
        }

        .ai-tool-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: var(--spacing-md);
            border: 1px solid var(--border-secondary);
            border-radius: var(--radius-sm);
            margin-bottom: var(--spacing-sm);
            background-color: var(--bg-primary);
            transition: all var(--duration-fast) var(--ease-out);
        }

        .ai-tool-item:hover {
            background-color: var(--bg-secondary);
            border-color: var(--border-primary);
        }

        .ai-tool-info {
            flex-grow: 1;
        }

        .ai-tool-name {
            font-weight: var(--font-weight-medium);
            color: var(--text-primary);
            margin-bottom: var(--spacing-xs);
        }

        .ai-tool-description {
            font-size: var(--font-size-sm);
            color: var(--text-secondary);
            line-height: var(--line-height-normal);
        }

        .ai-tool-toggle {
            margin-left: var(--spacing-md);
        }

        /* AI工具集增强样式 */
        .ai-tools-stats {
            background: var(--color-primary-light);
            border: 1px solid var(--color-primary-200);
            border-radius: var(--radius-sm);
            padding: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
            text-align: center;
        }

        .tools-stats-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .stats-label {
            font-weight: var(--font-weight-medium);
            color: var(--text-secondary);
        }

        .stats-value {
            font-weight: var(--font-weight-bold);
            color: var(--color-primary);
            font-size: var(--font-size-lg);
        }

        .ai-tools-category {
            margin: var(--spacing-lg) 0 var(--spacing-md) 0;
            border-bottom: 1px solid var(--border-secondary);
            padding-bottom: var(--spacing-sm);
        }

        .ai-tools-category:first-child {
            margin-top: 0;
        }

        .category-title {
            margin: 0;
            font-size: var(--font-size-base);
            font-weight: var(--font-weight-semibold);
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .category-count {
            font-size: var(--font-size-xs);
            font-weight: var(--font-weight-normal);
            color: var(--text-tertiary);
            margin-left: auto;
        }

        .ai-tool-item.enabled {
            border-color: var(--color-success-200);
            background-color: var(--color-success-light);
        }

        .ai-tool-item.disabled {
            opacity: 0.7;
            background-color: var(--color-gray-50);
        }

        .ai-tool-name {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .ai-tool-name i {
            color: var(--color-primary);
            width: 16px;
            text-align: center;
        }

        .tools-actions {
            display: flex;
            gap: var(--spacing-sm);
            flex-wrap: wrap;
            justify-content: center;
            padding-top: var(--spacing-lg);
            border-top: 1px solid var(--border-secondary);
        }

        /* === 文件上传状态样式 === */
        .file-upload-status {
            position: fixed;
            bottom: var(--spacing-xl);
            right: var(--spacing-xl);
            width: 320px;
            max-height: 400px;
            background-color: var(--bg-elevated);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-xl);
            z-index: var(--z-toast);
            transform: translateX(100%);
            transition: transform var(--duration-base) var(--ease-out);
        }

        .file-upload-status.visible {
            transform: translateX(0);
        }

        .file-status-content {
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        .file-status-header {
            padding: var(--spacing-md) var(--spacing-lg);
            border-bottom: 1px solid var(--border-secondary);
            display: flex;
            align-items: center;
            justify-content: space-between;
            background-color: var(--bg-secondary);
            border-radius: var(--radius-lg) var(--radius-lg) 0 0;
        }

        .file-status-title {
            font-weight: var(--font-weight-medium);
            color: var(--text-primary);
            font-size: var(--font-size-sm);
        }

        .file-status-close {
            background: none;
            border: none;
            color: var(--text-tertiary);
            cursor: pointer;
            padding: var(--spacing-xs);
            border-radius: var(--radius-sm);
            transition: all var(--duration-fast) var(--ease-out);
        }

        .file-status-close:hover {
            background-color: var(--color-gray-200);
            color: var(--text-primary);
        }

        .file-status-list {
            padding: var(--spacing-sm);
            flex-grow: 1;
            overflow-y: auto;
        }

        .file-status-item {
            padding: var(--spacing-sm) var(--spacing-md);
            border: 1px solid var(--border-secondary);
            border-radius: var(--radius-sm);
            margin-bottom: var(--spacing-sm);
            background-color: var(--bg-primary);
        }

        .file-status-item:last-child {
            margin-bottom: 0;
        }

        .file-status-item.success {
            border-color: var(--color-success);
            background-color: var(--color-success-light);
        }

        .file-status-item.error {
            border-color: var(--color-danger);
            background-color: var(--color-danger-light);
        }

        .file-status-item.uploading {
            border-color: var(--color-primary);
            background-color: var(--color-primary-light);
        }

        .file-item-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: var(--spacing-xs);
        }

        .file-item-name {
            font-weight: var(--font-weight-medium);
            color: var(--text-primary);
            font-size: var(--font-size-sm);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            flex-grow: 1;
            margin-right: var(--spacing-sm);
        }

        .file-item-size {
            font-size: var(--font-size-xs);
            color: var(--text-tertiary);
            white-space: nowrap;
        }

        .file-item-progress {
            width: 100%;
            height: 4px;
            background-color: var(--color-gray-200);
            border-radius: var(--radius-sm);
            overflow: hidden;
            margin-top: var(--spacing-xs);
        }

        .file-item-progress-bar {
            height: 100%;
            background-color: var(--color-primary);
            transition: width var(--duration-fast) var(--ease-out);
        }

        .file-item-status {
            font-size: var(--font-size-xs);
            margin-top: var(--spacing-xs);
            font-weight: var(--font-weight-medium);
        }

        .file-item-status.success {
            color: var(--color-success);
        }

        .file-item-status.error {
            color: var(--color-danger);
        }

        .file-item-status.uploading {
            color: var(--color-primary);
        }

        /* === 拖拽上传样式 === */
        #input-container.drag-over {
            background: linear-gradient(135deg,
                        rgba(37, 99, 235, 0.1) 0%,
                        rgba(37, 99, 235, 0.05) 100%);
            /* 移除边框颜色变化，保持简洁 */
            /* border-top-color: var(--color-primary); */
            transform: translateY(-2px);
            /* 添加微妙的阴影效果替代边框 */
            box-shadow: var(--shadow-xl),
                        inset 0 1px 0 rgba(255, 255, 255, 0.8),
                        0 0 0 2px rgba(37, 99, 235, 0.2);
        }

        /* 拖拽状态伪元素效果已移除 - 简化设计 */

        /* === 响应式设计优化 === */
        @media (max-width: 768px) {

            .function-panel-content {
                max-width: 95vw;
                margin: var(--spacing-sm);
            }

            .file-upload-status {
                width: 280px;
                bottom: var(--spacing-md);
                right: var(--spacing-md);
            }

            .voice-controls .btn {
                font-size: var(--font-size-sm);
                padding: var(--spacing-xs) var(--spacing-sm);
            }

            .tools-actions {
                flex-direction: column;
            }

            .tools-actions .btn {
                width: 100%;
            }
        }

        /* === 增强消息显示样式 === */

        /* 图片消息样式 */
        .message-image-container {
            margin: var(--spacing-sm) 0;
        }

        .image-preview {
            position: relative;
            display: inline-block;
            border-radius: var(--radius-md);
            overflow: hidden;
            box-shadow: var(--shadow-md);
            transition: all var(--duration-fast) var(--ease-out);
        }

        .image-preview:hover {
            box-shadow: var(--shadow-lg);
            transform: translateY(-1px);
        }

        .message-image {
            max-width: 300px;
            max-height: 200px;
            width: auto;
            height: auto;
            display: block;
            cursor: pointer;
            transition: all var(--duration-fast) var(--ease-out);
        }

        .image-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--spacing-sm);
            opacity: 0;
            transition: opacity var(--duration-fast) var(--ease-out);
        }

        .image-preview:hover .image-overlay {
            opacity: 1;
        }

        .image-action-btn {
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: var(--radius-full);
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-primary);
            cursor: pointer;
            transition: all var(--duration-fast) var(--ease-out);
        }

        .image-action-btn:hover {
            background: white;
            transform: scale(1.1);
        }

        .image-error {
            padding: var(--spacing-md);
            background: var(--color-danger-light);
            color: var(--color-danger);
            border-radius: var(--radius-sm);
            text-align: center;
            font-size: var(--font-size-sm);
        }

        /* 文件消息样式 */
        .message-file-container {
            margin: var(--spacing-sm) 0;
        }

        .file-preview {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            padding: var(--spacing-md);
            background: var(--bg-secondary);
            border: 1px solid var(--border-secondary);
            border-radius: var(--radius-md);
            transition: all var(--duration-fast) var(--ease-out);
        }

        .file-preview:hover {
            background: var(--bg-tertiary);
            border-color: var(--border-primary);
        }

        .file-icon {
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--color-primary-light);
            border-radius: var(--radius-md);
            color: var(--color-primary);
            font-size: var(--font-size-xl);
        }

        .file-info {
            flex-grow: 1;
            min-width: 0;
        }

        .file-name {
            font-weight: var(--font-weight-medium);
            color: var(--text-primary);
            margin-bottom: var(--spacing-xs);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .file-type {
            font-size: var(--font-size-xs);
            color: var(--text-tertiary);
            text-transform: uppercase;
        }

        .file-actions {
            display: flex;
            gap: var(--spacing-xs);
        }

        .file-action-btn {
            background: var(--bg-primary);
            border: 1px solid var(--border-secondary);
            border-radius: var(--radius-sm);
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-secondary);
            cursor: pointer;
            transition: all var(--duration-fast) var(--ease-out);
        }

        .file-action-btn:hover {
            background: var(--color-primary);
            border-color: var(--color-primary);
            color: white;
        }

        /* 音频消息样式 */
        .message-audio-container {
            margin: var(--spacing-sm) 0;
        }

        .audio-preview {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            padding: var(--spacing-md);
            background: var(--bg-secondary);
            border: 1px solid var(--border-secondary);
            border-radius: var(--radius-md);
            max-width: 350px;
        }

        .audio-icon {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--color-success-light);
            border-radius: var(--radius-full);
            color: var(--color-success);
            font-size: var(--font-size-lg);
        }

        .audio-controls {
            flex-grow: 1;
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .audio-play-btn {
            background: var(--color-success);
            border: none;
            border-radius: var(--radius-full);
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            cursor: pointer;
            transition: all var(--duration-fast) var(--ease-out);
        }

        .audio-play-btn:hover {
            background: var(--color-success-hover);
            transform: scale(1.05);
        }

        .audio-progress {
            flex-grow: 1;
            height: 4px;
            background: var(--color-gray-200);
            border-radius: var(--radius-sm);
            overflow: hidden;
            position: relative;
        }

        .audio-progress-bar {
            height: 100%;
            background: var(--color-success);
            width: 0%;
            transition: width var(--duration-fast) var(--ease-out);
        }

        .audio-time {
            font-size: var(--font-size-xs);
            color: var(--text-tertiary);
            font-family: var(--font-family-mono);
            min-width: 35px;
        }

        .audio-actions {
            display: flex;
            gap: var(--spacing-xs);
        }

        .audio-action-btn {
            background: var(--bg-primary);
            border: 1px solid var(--border-secondary);
            border-radius: var(--radius-sm);
            width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-secondary);
            cursor: pointer;
            transition: all var(--duration-fast) var(--ease-out);
        }

        .audio-action-btn:hover {
            background: var(--color-success);
            border-color: var(--color-success);
            color: white;
        }

        /* 视频消息样式 */
        .message-video-container {
            margin: var(--spacing-sm) 0;
        }

        .video-preview {
            position: relative;
            display: inline-block;
            border-radius: var(--radius-md);
            overflow: hidden;
            box-shadow: var(--shadow-md);
        }

        .message-video {
            max-width: 400px;
            max-height: 300px;
            width: auto;
            height: auto;
            display: block;
        }

        .video-actions {
            position: absolute;
            top: var(--spacing-sm);
            right: var(--spacing-sm);
            display: flex;
            gap: var(--spacing-xs);
            opacity: 0;
            transition: opacity var(--duration-fast) var(--ease-out);
        }

        .video-preview:hover .video-actions {
            opacity: 1;
        }

        .video-action-btn {
            background: rgba(0, 0, 0, 0.7);
            border: none;
            border-radius: var(--radius-sm);
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            cursor: pointer;
            transition: all var(--duration-fast) var(--ease-out);
        }

        .video-action-btn:hover {
            background: rgba(0, 0, 0, 0.9);
            transform: scale(1.05);
        }

        /* 视频全屏模态框 */
        .video-fullscreen-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.9);
            z-index: var(--z-modal);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: var(--spacing-lg);
        }

        .video-fullscreen-content {
            position: relative;
            max-width: 90vw;
            max-height: 90vh;
        }

        .video-fullscreen-close {
            position: absolute;
            top: -40px;
            right: 0;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: var(--radius-full);
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            cursor: pointer;
            transition: all var(--duration-fast) var(--ease-out);
        }

        .video-fullscreen-close:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .video-fullscreen {
            max-width: 100%;
            max-height: 100%;
            width: auto;
            height: auto;
        }

        /* 图片查看器模态框 */
        .image-viewer-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.9);
            z-index: var(--z-modal);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: var(--spacing-lg);
            opacity: 0;
            animation: fadeIn var(--duration-base) var(--ease-out) forwards;
        }

        @keyframes fadeIn {
            to {
                opacity: 1;
            }
        }

        .image-viewer-content {
            position: relative;
            max-width: 95vw;
            max-height: 95vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: var(--spacing-lg);
        }

        .image-viewer-close {
            position: absolute;
            top: -50px;
            right: 0;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: var(--radius-full);
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            cursor: pointer;
            font-size: var(--font-size-lg);
            transition: all var(--duration-fast) var(--ease-out);
        }

        .image-viewer-close:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        .image-viewer-img {
            max-width: 100%;
            max-height: calc(95vh - 80px);
            width: auto;
            height: auto;
            border-radius: var(--radius-md);
            box-shadow: var(--shadow-2xl);
            transition: transform var(--duration-base) var(--ease-out);
        }

        .image-viewer-img:hover {
            transform: scale(1.02);
        }

        .image-viewer-actions {
            display: flex;
            gap: var(--spacing-md);
            background: rgba(255, 255, 255, 0.1);
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--radius-lg);
            backdrop-filter: blur(10px);
        }

        .image-viewer-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: var(--radius-md);
            padding: var(--spacing-sm) var(--spacing-md);
            color: white;
            cursor: pointer;
            font-size: var(--font-size-sm);
            font-weight: var(--font-weight-medium);
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            transition: all var(--duration-fast) var(--ease-out);
        }

        .image-viewer-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-1px);
        }

        .image-viewer-btn:active {
            transform: translateY(0);
        }

        /* 响应式优化 */
        @media (max-width: 768px) {
            .image-viewer-close {
                top: -40px;
                width: 36px;
                height: 36px;
                font-size: var(--font-size-base);
            }

            .image-viewer-actions {
                flex-direction: column;
                gap: var(--spacing-sm);
                padding: var(--spacing-sm) var(--spacing-md);
            }

            .image-viewer-btn {
                justify-content: center;
                padding: var(--spacing-sm);
            }

            .message-image {
                max-width: 250px;
                max-height: 150px;
            }

            .file-preview {
                padding: var(--spacing-sm);
                gap: var(--spacing-sm);
            }

            .file-icon {
                width: 40px;
                height: 40px;
                font-size: var(--font-size-lg);
            }

            .audio-preview {
                max-width: 100%;
                padding: var(--spacing-sm);
            }

            .message-video {
                max-width: 100%;
                max-height: 200px;
            }
        }

        /* === 一体化输入容器设计 - 现代极简 === */
        #input-textarea-wrapper {
            position: relative;
            display: flex;
            flex-direction: column;
            /* 使用设计系统变量 */
            border: 1px solid var(--input-border);
            border-radius: var(--input-radius);
            background: var(--input-bg);
            backdrop-filter: var(--blur-md);
            -webkit-backdrop-filter: var(--blur-md);
            box-shadow: var(--card-shadow);
            /* 统一过渡动画 */
            transition:
                border-color var(--duration-fast) var(--ease-out),
                box-shadow var(--duration-fast) var(--ease-out);
            z-index: var(--z-content);
            overflow: hidden;
            min-height: 44px;
        }

        #input-textarea-wrapper:hover {
            /* 优化悬停状态配色 */
            border-color: rgba(59, 130, 246, 0.6);
            background: rgba(255, 255, 255, 1);
            box-shadow:
                0 4px 12px rgba(0, 0, 0, 0.08),
                0 2px 4px rgba(59, 130, 246, 0.1);
        }

        #input-textarea-wrapper:focus-within {
            /* 优化聚焦状态配色 - 简化阴影避免锯齿 */
            border-color: var(--input-border-focus);
            border-width: 1.5px;
            background: rgba(255, 255, 255, 1);
            /* 简化阴影效果，避免边角锯齿 */
            box-shadow:
                0 4px 12px rgba(0, 0, 0, 0.08),
                0 0 0 2px rgba(59, 130, 246, 0.12);
            /* 优化边框渲染 */
            border-style: solid;
            box-sizing: border-box;
        }

        /* 移除复杂的底部过渡效果 - 简化设计 */

        /* 重新设计的消息输入框 */
        #message-input {
            flex-grow: 1;
            min-height: 44px;
            max-height: 150px;
            padding: var(--spacing-md) var(--spacing-lg);
            border: none;
            background: transparent;
            color: var(--text-primary);
            font-size: var(--font-size-base);
            line-height: var(--line-height-normal);
            resize: none;
            overflow-y: auto;
            outline: none;
            font-family: var(--font-family-base);
            transition: all var(--duration-fast) var(--ease-out);
        }

        #message-input:focus {
            outline: none;
        }

        #message-input::placeholder {
            color: var(--text-muted);
            font-style: italic;
        }

        /* === 优化工具栏设计 - 协调配色 === */
        #input-toolbar {
            display: flex;
            align-items: center;
            justify-content: space-between;
            /* 优化工具栏配色协调性 */
            padding: var(--toolbar-padding);
            border-top: 1px solid rgba(209, 213, 219, 0.6);
            background: linear-gradient(180deg,
                rgba(248, 250, 252, 0.98) 0%,
                rgba(241, 245, 249, 0.95) 100%);
            min-height: var(--toolbar-height);
            z-index: var(--z-elevated);
        }

        /* 移除复杂的工具栏过渡效果 - 简化设计 */

        #toolbar-left {
            display: flex;
            align-items: center;
            /* 黄金比例间距 */
            gap: var(--spacing-button-gap);
        }

        #toolbar-right {
            display: flex;
            align-items: center;
            /* 发送按钮的左边距 */
            margin-left: var(--spacing-md);
        }

        /* === 现代极简工具栏按钮设计 === */
        .toolbar-button {
            /* 统一按钮样式 - 与其他按钮保持一致 */
            width: 36px;
            height: 36px;
            min-height: 36px;
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-lg);
            background: var(--bg-surface);
            color: var(--text-secondary);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all var(--duration-fast) var(--ease-out);
            font-size: var(--font-size-sm);
            font-weight: var(--font-weight-medium);
            line-height: var(--line-height-tight);
            box-shadow: var(--shadow-xs);
            z-index: var(--z-elevated);
            user-select: none;
        }

        /* 工具栏按钮悬停效果已迁移到 design-system.css 统一管理 */

        /* === 简化的开关按钮设计 === */
        .toolbar-toggle-button {
            /* 确保触摸友好性 */
            width: 36px;
            height: 36px;
            border: none;
            border-radius: var(--radius-md);
            background: transparent;
            color: var(--text-tertiary);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            /* 优化的过渡动画 - 仅颜色变化 */
            transition: color var(--duration-medium) var(--ease-natural),
                        transform var(--duration-fast) var(--ease-out);
            font-size: var(--font-size-base);
            position: relative;
        }

        .toolbar-toggle-button:hover {
            /* 微妙的悬停效果，无背景 */
            transform: translateY(-1px) scale(1.05);
        }

        .toolbar-toggle-button:active {
            transform: translateY(0) scale(0.95);
        }

        .toolbar-toggle-button:focus {
            outline: none;
            /* 简化的焦点样式 */
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
        }

        /* === 简化的开关状态设计 - 纯图标颜色变化 === */
        .toolbar-toggle-button[data-enabled="false"] {
            /* 关闭状态：灰色图标，无背景 */
            background: transparent;
            color: var(--text-tertiary);
        }

        .toolbar-toggle-button[data-enabled="false"]:hover {
            /* 悬停时稍微加深颜色 */
            color: var(--text-secondary);
        }

        .toolbar-toggle-button[data-enabled="true"] {
            /* 开启状态：主题色图标，无背景 */
            background: transparent;
            color: var(--color-primary);
        }

        .toolbar-toggle-button[data-enabled="true"]:hover {
            /* 悬停时稍微加深主题色 */
            color: var(--color-primary-600);
        }

        /* === 功能特定的开关按钮颜色主题 === */

        /* 智能分析按钮 - 黄色/橙色主题（灯泡概念） */
        #tts-toggle-button[data-enabled="true"] {
            background: transparent;
            color: var(--color-warning);
        }

        #tts-toggle-button[data-enabled="true"]:hover {
            color: var(--color-warning-hover);
        }

        /* 网络搜索按钮 - 蓝色主题 */
        #web-search-toggle-button[data-enabled="true"] {
            background: transparent;
            color: var(--color-primary);
        }

        #web-search-toggle-button[data-enabled="true"]:hover {
            color: var(--color-primary-hover);
        }

        /* 移除开关按钮的伪元素，确保纯图标设计 */
        .toolbar-toggle-button::before {
            display: none !important;
        }

        /* === 响应式设计优化 === */

        /* 平板端优化 (768px - 1024px) */
        @media (max-width: 1024px) and (min-width: 769px) {
            .toolbar-button,
            .toolbar-toggle-button {
                width: 34px;
                height: 34px;
            }

            #send-button {
                width: 38px;
                height: 38px;
            }

            #input-toolbar {
                padding: var(--spacing-sm) var(--spacing-lg);
            }
        }

        /* 移动端优化 (≤768px) */
        @media (max-width: 768px) {
            .toolbar-button,
            .toolbar-toggle-button {
                width: 32px;
                height: 32px;
                font-size: var(--font-size-sm);
                /* 减少移动端的变换效果，提高性能 */
                transform: none !important;
            }

            .toolbar-button:hover,
            .toolbar-toggle-button:hover {
                transform: none !important;
            }

            /* 移动端开关按钮保持颜色变化 */
            .toolbar-toggle-button[data-enabled="false"] {
                color: var(--text-tertiary);
            }

            #tts-toggle-button[data-enabled="true"] {
                color: #f59e0b;
            }

            #web-search-toggle-button[data-enabled="true"] {
                color: #3b82f6;
            }

            #send-button {
                width: 36px;
                height: 36px;
                font-size: var(--font-size-base);
            }

            #toolbar-left {
                gap: var(--spacing-sm);
            }

            #input-toolbar {
                padding: var(--spacing-sm) var(--spacing-md);
                min-height: 48px;
            }

            #message-input {
                padding: var(--spacing-sm) var(--spacing-md);
                min-height: 40px;
            }

            /* 优化移动端的阴影效果 */
            #input-textarea-wrapper {
                box-shadow: var(--shadow-sm);
            }

            #input-textarea-wrapper:hover,
            #input-textarea-wrapper:focus-within {
                /* 移动端简化阴影效果 */
                box-shadow: var(--shadow-md);
            }
        }

        /* 超小屏幕优化 (≤480px) */
        @media (max-width: 480px) {
            .toolbar-button,
            .toolbar-toggle-button {
                width: 28px;
                height: 28px;
                font-size: var(--font-size-xs);
                border-radius: var(--radius-sm);
            }

            #send-button {
                width: 32px;
                height: 32px;
                font-size: var(--font-size-sm);
            }

            #toolbar-left {
                gap: var(--spacing-xs);
            }

            #input-toolbar {
                padding: var(--spacing-xs) var(--spacing-sm);
                min-height: 44px;
            }

            #message-input {
                padding: var(--spacing-xs) var(--spacing-sm);
                min-height: 36px;
                font-size: var(--font-size-sm);
            }

            /* 进一步简化超小屏幕的视觉效果 */
            #input-textarea-wrapper {
                border-radius: var(--radius-xl);
                backdrop-filter: blur(8px);
            }

            .toolbar-button::before,
            .toolbar-toggle-button::before {
                display: none;
            }
        }

        /* === 无障碍和高对比度优化 === */

        /* 高对比度模式支持 */
        @media (prefers-contrast: high) {
            #input-textarea-wrapper {
                border-width: 2px;
                border-color: var(--text-primary);
                background: var(--bg-primary);
                backdrop-filter: none;
            }

            .toolbar-button,
            .toolbar-toggle-button {
                border: 1px solid var(--text-tertiary);
            }

            /* 高对比度模式下的开关按钮 */
            .toolbar-toggle-button[data-enabled="false"] {
                color: var(--text-tertiary);
                background: transparent;
            }

            .toolbar-toggle-button[data-enabled="true"] {
                color: var(--text-primary);
                background: transparent;
                border-color: var(--text-primary);
                /* 确保足够的对比度 */
                font-weight: var(--font-weight-bold);
            }

            #send-button {
                border: 2px solid var(--color-primary);
            }
        }

        /* 减少动画偏好支持 */
        @media (prefers-reduced-motion: reduce) {
            #input-textarea-wrapper,
            .toolbar-button,
            .toolbar-toggle-button,
            #send-button {
                transition: none;
                animation: none;
            }

            .toolbar-button:hover,
            .toolbar-toggle-button:hover,
            #send-button:hover {
                transform: none;
            }

            #send-button::before {
                display: none;
            }
        }

        /* 焦点可见性增强 - 现代化脉动效果 */
        @media (prefers-reduced-motion: no-preference) {
            .toolbar-button:focus-visible,
            .toolbar-toggle-button:focus-visible,
            #send-button:focus-visible {
                animation: modern-focus-pulse 2s infinite;
            }
        }

        @keyframes modern-focus-pulse {
            0%, 100% {
                box-shadow: 0 0 0 var(--focus-glow-radius) var(--focus-glow-color);
            }
            50% {
                box-shadow: 0 0 0 calc(var(--focus-glow-radius) + 2px) rgba(100, 181, 246, 0.15);
            }
        }

        /* === 性能优化 === */

        /* 硬件加速优化 */
        #input-textarea-wrapper,
        .toolbar-button,
        .toolbar-toggle-button,
        #send-button {
            will-change: transform, box-shadow;
            transform: translateZ(0);
        }

        /* 避免重排重绘的优化 */
        .toolbar-button:hover,
        .toolbar-toggle-button:hover,
        #send-button:hover {
            will-change: transform, box-shadow, background;
        }

        /* 复合层优化 */
        #input-toolbar {
            contain: layout style paint;
        }

        /* 字体渲染优化 */
        #input-textarea-wrapper,
        #input-toolbar {
            text-rendering: optimizeLegibility;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* GPU 加速的模糊效果 */
        #input-textarea-wrapper {
            transform: translate3d(0, 0, 0);
        }

        /* === 精致化发送按钮设计 === */
        #send-button {
            /* 更大的点击区域，确保触摸友好性 */
            width: 40px;
            height: 40px;
            /* 精致的渐变背景 */
            background: linear-gradient(135deg,
                        var(--color-primary) 0%,
                        var(--color-primary-600) 100%);
            color: white;
            border: 1px solid var(--color-primary-400);
            border-radius: var(--radius-full);
            cursor: pointer;
            font-size: var(--font-size-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
            /* 优化过渡动画 - 指定具体属性 */
            transition:
                background var(--duration-medium) var(--ease-natural),
                box-shadow var(--duration-fast) var(--ease-out),
                transform var(--duration-fast) var(--ease-out);
            margin-left: var(--spacing-md);
            /* 精致的阴影效果 */
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.25),
                        inset 0 1px 0 rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
            /* 设置层级和独立合成层 */
            z-index: var(--z-floating);
            isolation: isolate;
        }

        #send-button::before {
            /* 微妙的光泽效果 */
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                        transparent 0%,
                        rgba(255, 255, 255, 0.2) 50%,
                        transparent 100%);
            transition: left var(--duration-slow) var(--ease-out);
        }

        #send-button:hover:not(:disabled):not(.abort-active) {
            background: linear-gradient(135deg,
                        var(--color-primary-600) 0%,
                        var(--color-primary-700) 100%);
            box-shadow: 0 4px 12px var(--color-primary-alpha-30),
                        inset 0 1px 0 rgba(255, 255, 255, 0.2);
            transform: translateY(-1px) scale(1.02);
            /* 添加微妙的发光动画 */
            animation: lightGlow 2s ease-in-out infinite;
        }

        #send-button:hover:not(:disabled):not(.abort-active)::before {
            left: 100%;
        }

        #send-button:focus {
            outline: none;
            box-shadow: var(--shadow-focus),
                        0 2px 8px rgba(59, 130, 246, 0.25);
        }

        #send-button:active:not(:disabled) {
            transform: translateY(0) scale(0.98);
            box-shadow: 0 1px 4px rgba(59, 130, 246, 0.3);
        }

        #send-button.abort-active {
            background: linear-gradient(135deg,
                        var(--color-danger) 0%,
                        var(--color-danger-active) 100%);
            border-color: var(--color-danger);
            box-shadow: var(--shadow-button-danger);
        }

        #send-button.abort-active:hover {
            background: linear-gradient(135deg,
                        var(--color-danger-active) 0%,
                        var(--color-danger-dark) 100%);
            box-shadow: var(--shadow-button-danger-hover);
        }

        #send-button:disabled {
            background: var(--color-gray-400);
            border-color: var(--color-gray-300);
            cursor: not-allowed;
            opacity: 0.6;
            box-shadow: none;
            transform: none;
        }

        #send-button i {
            font-size: 0.9em;
            transition: transform var(--duration-fast) var(--ease-out);
        }

        #send-button i.fa-spinner {
            animation: fa-spin 2s infinite linear;
        }

        @keyframes fa-spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }


        /* === 空状态样式 === */

        #empty-state-active {
            display: flex !important;
            flex-direction: column !important;
            justify-content: center !important;
            align-items: center !important;
            text-align: center !important;
            padding: 40px 20px;
            color: var(--text-tertiary);
            height: 100%;
            width: 100%;
            box-sizing: border-box;
            overflow: hidden;
            opacity: 0;
            animation: emptyStateSlideIn var(--duration-medium) var(--ease-out) forwards;
        }

        @keyframes emptyStateSlideIn {
            from {
                opacity: 0;
                transform: translateY(20px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }
        #empty-state-active .logo-container { width: 80px; height: 88px; margin: 0 auto 25px auto; flex-shrink: 0; position: relative; background-color: transparent; border-radius: 0; box-shadow: none; overflow: visible; }
        #empty-state-active .logo { width: 100%; height: 100%; display: block; }
        #empty-state-active .welcome-title { font-size: 1.6em; font-weight: 600; margin-bottom: 12px; color: var(--text-primary); width: 100%; text-align: center; flex-shrink: 0; }
        #empty-state-active .model-name-display { font-size: 1.0em; color: #495057; margin-bottom: 18px; font-weight: 500; width: 90%; text-align: center; word-break: break-word; flex-shrink: 0; }
        #empty-state-active .welcome-message { font-size: 1.0em; max-width: 90%; margin: 0 auto 30px auto; line-height: 1.6; text-align: center; flex-shrink: 0; }
        #empty-state-active .suggestion-buttons { display: flex; flex-wrap: wrap; justify-content: center; gap: 12px; width: 100%; max-width: 450px; margin: 0 auto; flex-shrink: 0; }
        #empty-state-active .suggestion-button {
            /* 统一按钮样式 - 与侧边栏按钮保持一致 */
            background: var(--bg-surface);
            border: 1px solid var(--border-primary);
            color: var(--text-secondary);
            padding: var(--spacing-sm) var(--spacing-lg);
            border-radius: var(--radius-lg);
            cursor: pointer;
            font-size: var(--font-size-sm);
            font-weight: var(--font-weight-medium);
            line-height: var(--line-height-tight);
            transition: all var(--duration-fast) var(--ease-out);
            box-shadow: var(--shadow-xs);
            flex-grow: 1;
            min-width: 140px;
            min-height: 36px;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            white-space: nowrap;
        }
        @media (min-width: 480px) { #empty-state-active .suggestion-button { flex-basis: 45%; } }
        #empty-state-active .suggestion-button:hover {
            background: var(--bg-hover, var(--color-gray-100));
            border-color: var(--border-secondary);
            color: var(--text-primary);
            box-shadow: var(--shadow-sm);
            transform: translateY(-0.5px);
        }

        #empty-state-active .suggestion-button:active {
            background: var(--bg-active, var(--color-gray-200));
            border-color: var(--border-tertiary);
            transform: translateY(0);
            box-shadow: var(--shadow-xs);
        }

        #empty-state-active .suggestion-button:focus {
            outline: none;
        }

        #empty-state-active .suggestion-button:focus-visible {
            outline: 2px solid var(--focus-border-color);
            outline-offset: 2px;
            box-shadow: 0 0 0 var(--focus-glow-radius) var(--focus-glow-color);
            transition: var(--focus-transition);
        }

        /* === 思考过程容器样式系统 - 统一定义 === */

        /* 基础容器样式 */
        .thinking-process-container {
            background-color: var(--bg-subtle);
            border: 1px solid var(--border-secondary);
            border-radius: var(--radius-md);
            margin: var(--spacing-md) 0;
            padding: 0;
            overflow: hidden;
            position: relative;
            transition: all 0.3s ease;
        }

        /* 头部样式 */
        .thinking-process-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-sm) var(--spacing-md);
            background-color: var(--bg-secondary);
            cursor: pointer;
            border-bottom: 1px solid var(--border-secondary);
            transition: background-color 0.2s ease;
        }

        /* 标题样式 */
        .thinking-process-title {
            font-weight: 500;
            font-size: 0.95em;
            color: var(--text-secondary);
            flex-grow: 1;
            display: flex;
            align-items: center;
            transition: color 0.2s ease;
        }

        .thinking-process-title .fa-lightbulb {
            margin-right: 8px;
            color: var(--color-warning);
            font-size: 1.1em;
            transition: color 0.2s ease;
        }

        .thinking-status {
            margin-left: 5px;
            position: relative;
        }

        /* 活动状态样式 */
        .thinking-process-title.thinking-active {
            color: var(--color-primary);
        }

        .thinking-process-title.thinking-active .thinking-status::after {
            content: var(--thinking-dots, ".");
            display: inline-block;
            margin-left: 1px;
        }

        .thinking-process-title.thinking-active .fa-lightbulb {
            color: var(--color-warning);
        }

        /* 统一流光效果系统 */
        .thinking-process-container.thinking-active {
            position: relative;
            background: var(--thinking-active-bg, linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%));
            border-color: var(--thinking-active-border, #007bff);
            box-shadow: var(--thinking-active-shadow, 0 4px 20px rgba(0, 123, 255, 0.15));
        }

        .thinking-process-container.thinking-active::before {
            content: '';
            position: absolute;
            top: 0;
            left: var(--flow-light-position, -100%);
            width: 100%;
            height: 100%;
            background: var(--thinking-flow-gradient, linear-gradient(90deg,
                transparent 0%,
                rgba(0, 123, 255, 0.1) 25%,
                rgba(0, 123, 255, 0.2) 50%,
                rgba(255, 193, 7, 0.2) 75%,
                transparent 100%));
            opacity: var(--flow-light-opacity, 0);
            pointer-events: none;
            z-index: 1;
            transition: opacity 0.1s ease;
        }

        /* 计时器样式 */
        .thinking-time {
            font-size: 0.85em;
            color: var(--text-tertiary);
            margin-right: 10px;
            font-weight: normal;
            background-color: var(--thinking-time-bg, rgba(243, 244, 246, 0.7));
            padding: 2px 6px;
            border-radius: 12px;
            border: 1px solid var(--thinking-time-border, #e5e7eb);
            transition: all 0.2s ease;
        }

        .thinking-process-title.thinking-active + .thinking-time {
            color: var(--thinking-time-active-color, #1a56db);
            border-color: var(--thinking-time-active-border, #bfdbfe);
            background-color: var(--thinking-time-active-bg, rgba(219, 234, 254, 0.4));
        }

        .thinking-time-counter {
            display: inline-block;
            font-weight: 500;
        }

        /* 切换按钮样式 */
        .thinking-process-toggle {
            background: none;
            border: none;
            font-size: 0.95em;
            color: var(--text-tertiary);
            cursor: pointer;
            padding: 4px 6px;
            line-height: 1;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .thinking-process-toggle:hover {
            background-color: var(--thinking-toggle-hover-bg, rgba(209, 213, 219, 0.3));
            color: var(--text-secondary);
        }

        .thinking-process-toggle .fa-chevron-down {
            transition: transform 0.3s ease-in-out;
        }

        .thinking-process-header.collapsed .thinking-process-toggle .fa-chevron-down {
            transform: rotate(-90deg);
        }

        /* 内容区域样式 */
        .thinking-process-content {
            padding: 12px 16px;
            font-size: 0.9em;
            line-height: 1.6;
            color: var(--text-secondary);
            max-height: 350px;
            overflow-y: auto;
            background-color: var(--bg-primary);
            transition: all 0.3s ease;
        }

        .thinking-process-header.collapsed + .thinking-process-content {
            display: none;
        }

        .thinking-process-content p {
            margin: 0 0 10px 0;
        }

        .thinking-process-content p:last-child {
            margin-bottom: 0;
        }

        .thinking-process-content strong {
            color: var(--text-primary);
            font-weight: 600;
        }

        /* 思考过程内容区域滚动条隐藏 - 已通过全局设置处理 */

        /* --- Mermaid Error Handling Styles --- */
        .mermaid-error-container {
            background: var(--color-warning-light);
            border: 1px solid var(--color-warning);
            border-radius: 6px;
            padding: 12px;
            margin: 8px 0;
            transition: opacity 0.3s ease;
        }

        .mermaid-error-message {
            color: var(--color-warning-hover);
            font-size: 0.9em;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .mermaid-error-message i {
            color: var(--color-warning);
        }

        .mermaid-error-container .mermaid-code-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            overflow: hidden;
        }

        .mermaid-error-container .mermaid-code-display pre {
            margin: 0;
            padding: 8px;
            font-size: 0.85em;
            background: none;
            border: none;
            max-height: 200px;
            overflow-y: auto;
            color: #495057;
        }

        /* 防止闪烁的过渡效果 */
        .mermaid-diagram-view {
            transition: opacity 0.2s ease;
        }

        .mermaid-container {
            position: relative;
        }

        /* 渲染中的状态 */
        .mermaid-rendering {
            opacity: 0.7;
        }

        .mermaid-rendering::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.9em;
            color: #6c757d;
            z-index: 10;
        }

        /* 防闪烁状态样式 - 浅色主题 */
        .mermaid-pending {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            text-align: center;
            color: #6c757d;
            font-size: 0.9em;
            min-height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .mermaid-loading {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            text-align: center;
            color: #6c757d;
            font-size: 0.9em;
            min-height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .instant-rendering {
            opacity: 0.5;
            transition: opacity 0.3s ease;
        }

        .instant-success {
            opacity: 1;
            transition: opacity 0.3s ease;
        }

        .instant-error {
            opacity: 1;
        }

        .mermaid-success {
            opacity: 1;
        }

        /* --- Message Actions & Meta --- */
        /* 消息操作按钮样式已在新拟态交互效果中重新定义 */
        .message-container.user-message-container .message-actions { margin-left: auto; }
        .message-action-button {
            background: none;
            border: 0.5px solid transparent;
            color: var(--text-secondary);
            cursor: pointer;
            padding: 6px 10px;
            font-size: 13px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 6px;
            border-radius: 6px;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        }
        .message-action-button:hover {
            color: var(--text-primary);
            background-color: var(--color-gray-100);
            border-color: var(--color-gray-300);
            transform: translateY(-0.5px);
        }
        .message-action-button i { font-size: 13px; }
        .message-action-button .fa-check { color: var(--color-success); }
        .message-meta-info { font-size: 0.7em; color: #9ca3af; margin-top: 4px; padding: 0 12px; text-align: right; }
        .message-container.assistant-message-container .message-meta-info { text-align: left; }

        /* --- Message Bubbles --- */
        .message-container {
            display: flex;
            align-items: baseline;
            margin-bottom: var(--spacing-md);
            gap: var(--spacing-sm);
            opacity: 0;
            transform: translateY(10px);
            animation: messageSlideIn var(--duration-medium) var(--ease-out) forwards;
            position: relative;
        }
        .message-container.user-message-container { flex-direction: row-reverse; }
        .message-container.assistant-message-container { flex-direction: row; }
        .message-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            flex-shrink: 0;
            background-color: var(--bg-secondary);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.9em;
            color: var(--text-secondary);
            border: 1px solid var(--border-secondary);
        }
        .message-bubble-wrapper { display: flex; flex-direction: column; max-width: 85%; }
        .message-container.user-message-container .message-bubble-wrapper { align-items: flex-end; }
        .message-container.assistant-message-container .message-bubble-wrapper { align-items: flex-start; }

        /* 消息气泡基础样式 - 扁平化无边界设计 */
        .message-bubble {
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--radius-lg);
            word-wrap: break-word;
            line-height: 1.6;
            position: relative;
            width: fit-content;
            min-width: 40px;
            max-width: 100%;
            /* 默认无边界状态 */
            border: none;
            box-shadow: none;
            /* 优化过渡属性，包含 margin 变化 */
            transition: box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                       margin 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                       opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            /* 文字渲染优化 */
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* 用户消息气泡 */
        .message-container.user-message-container .message-bubble {
            background: var(--color-primary-light);
            color: var(--text-primary);
            border-bottom-right-radius: var(--radius-sm);
        }

        /* AI消息气泡 - 保持透明无色设计 */
        .message-container.assistant-message-container .message-bubble {
            background: transparent;
            color: var(--text-primary);
        }
        .message-container.assistant-message-container .message-bubble {
            background: transparent;  /* 无色背景，零边界设计 */
            color: var(--text-primary);
            border: none;
            border-bottom-left-radius: var(--radius-sm);
            border-top-left-radius: var(--radius-lg);
        }
        .error-message .message-bubble, .api-error-message .message-bubble { width: 100%; border-radius: 6px; padding-left: 15px !important; border-left: 4px solid #dc3545; background-color: #f8d7da; color: #721c24; }
        .api-error-message .message-bubble { background-color: #fff3e0; color: #e65100; border-left-color: #ff9800; }
        .message-status-text { font-size: 0.75em; color: #888; margin-top: 3px; padding: 0 8px; opacity: 0.8; display: block; }

        /* AI消息底部元数据样式 */
        .ai-message-footer {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            margin-top: 4px;
            font-size: 0.75rem;
            color: var(--text-tertiary);
            opacity: 0.7;
            transition: opacity 0.2s ease;
        }

        .ai-message-footer:hover {
            opacity: 1;
        }

        .ai-message-footer .footer-model-name {
            font-weight: 500;
            color: var(--color-primary);
            background: var(--color-primary-light);
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 0.7rem;
        }

        .ai-message-footer .footer-timestamp {
            color: var(--text-secondary);
            font-family: var(--font-mono);
        }

        .ai-message-footer .footer-image-seed {
            color: var(--text-secondary);
            font-family: var(--font-mono);
            background: var(--bg-tertiary);
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 0.7rem;
        }

        .ai-message-footer .footer-separator {
            color: var(--text-tertiary);
            opacity: 0.5;
        }

        /* 批量图片容器样式 */
        .batch-images-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-sm);
            margin-top: var(--spacing-sm);
        }

        .batch-images-container .generated-image-container.batch-image {
            max-width: 100%;
        }

        .batch-images-container .generated-image {
            width: 100%;
            height: auto;
            border-radius: var(--border-radius);
        }
        @keyframes highlightEditAnim { 0% { background-color: #fff3cd; border-color: #ffeeba; } 50% { background-color: #fff3cd; border-color: #ffeeba; } 100% {} }
        @keyframes highlightEditUserAnim { 0% { background-color: #cce5ff; border-color: #b8daff; } 50% { background-color: #cce5ff; border-color: #b8daff; } 100% { background-color: #e7f0ff; border-color: #d0e0ff; } }
        .message-bubble.highlight-edit { animation-duration: 1.5s; }
        .user-message-container .message-bubble.highlight-edit { animation-name: highlightEditUserAnim; }
        .assistant-message-container .message-bubble.highlight-edit { animation-name: highlightEditAnim; }
        /* === 消息动画系统 === */

        @keyframes messageSlideIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        .message-content { order: 0; width: 100%; overflow-wrap: break-word; word-break: break-word; }


        .message-content p, .message-content ul, .message-content ol, .message-content blockquote { margin-bottom: 0.8em; }
        .message-content p:last-child { margin-bottom: 0; }
        .message-content ul, .message-content ol { padding-left: 1.5em; }
        .message-content blockquote { border-left: 3px solid #ccc; padding-left: 1em; margin-left: 0; color: #555; }
        .message-content a { color: var(--text-link); text-decoration: none; word-break: break-all; }
        .message-content a:hover { text-decoration: underline; }
        .message-content code:not(pre code) { background-color: #e9ecef; padding: 0.2em 0.4em; border-radius: 3px; font-size: 90%; }




        .message-content img {
            max-width: 400px;
            max-height: 400px;
            width: auto;
            height: auto;
            border-radius: var(--radius-sm);
            margin-top: var(--spacing-xs);
            margin-bottom: var(--spacing-xs);
            object-fit: contain;
        }
        .message-content .generated-image-container {
            margin: var(--spacing-sm) 0;
            position: relative;
            display: inline-block;
            max-width: 400px;
        }
        .message-content .generated-image-container img {
            max-width: 100%;
            max-height: 400px;
            width: auto;
            height: auto;
            border-radius: var(--radius-md);
            border: 1px solid var(--border-secondary);
            display: block;
            cursor: pointer;
            object-fit: contain;
        }
        /* 图片操作按钮样式 - 仅用于生成的图片 */
        .image-action-buttons {
            position: absolute;
            top: -32px;
            right: 4px;
            display: flex;
            gap: 6px;
            opacity: 0;
            transition: opacity 0.2s ease;
            pointer-events: none;
            z-index: 10;
            background: rgba(0, 0, 0, 0.8);
            padding: 4px 8px;
            border-radius: 16px;
            backdrop-filter: blur(4px);
        }
        .message-content .generated-image-container:hover .image-action-buttons {
            opacity: 1;
            pointer-events: auto;
        }
        .image-action-button {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            width: 24px;
            height: 24px;
            font-size: 0.7em;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }
        .image-action-button:hover {
            background-color: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }
        .image-save-button {
            background-color: var(--color-success) !important;
            border-color: var(--color-success) !important;
        }
        .image-save-button:hover {
            background-color: var(--color-success-hover) !important;
            border-color: var(--color-success-hover) !important;
        }


        /* === 消息气泡新拟态交互效果 === */

        /* 消息操作按钮基础样式 */
        .message-actions {
            order: 1;
            display: flex;
            justify-content: flex-end;
            gap: var(--spacing-xs);
            margin-top: 0;
            padding: var(--spacing-xs) 0 0 0;
            max-height: 10px;
            opacity: 0;
            overflow: visible;
            /* 优化过渡，避免影响文字布局 */
            transition: max-height 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                       opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                       margin-top 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 100;
            /* 确保操作按钮不影响文字渲染 */
            will-change: opacity, max-height;
            contain: layout style;
        }

        /* 消息气泡悬停效果 - 隐约浮现边界轮廓 */
        @media (hover: hover) and (pointer: fine) {
            .message-bubble:hover {
                /* 新拟态风格：轻微内阴影和外轮廓 */
                box-shadow:
                    inset 0 1px 2px rgba(0, 0, 0, 0.05),
                    0 0 0 1px rgba(0, 0, 0, 0.08),
                    0 2px 8px rgba(0, 0, 0, 0.04);
                transform: translateY(-1px);
            }

            .message-bubble:hover .message-actions {
                max-height: 60px;
                opacity: 1;
                margin-top: var(--spacing-xs);
            }
        }

        /* 消息气泡选中状态 - 优化视觉稳定性 */
        .message-bubble.actions-visible {
            /* 选中时的新拟态效果：更明显的边界轮廓和呼吸感 */
            box-shadow:
                inset 0 2px 4px rgba(0, 0, 0, 0.08),
                0 0 0 1px rgba(0, 0, 0, 0.12),
                0 4px 12px rgba(0, 0, 0, 0.08),
                0 0 20px rgba(0, 0, 0, 0.02);
            /* 移除 translateY 以避免文字抖动，改用 margin 实现视觉提升效果 */
            margin-top: -2px;
            margin-bottom: 2px;
            /* 渲染优化属性 */
            will-change: box-shadow;
            transform-origin: center center;
            /* 文字渲染优化 */
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            /* 启用硬件加速但避免 transform 导致的文字抖动 */
            backface-visibility: hidden;
            animation: message-bubble-breathe 2s ease-in-out infinite;
        }

        .message-bubble.actions-visible .message-actions {
            max-height: 60px;
            opacity: 1;
            margin-top: var(--spacing-xs);
        }

        /* 优化的呼吸感动画 - 只变化 box-shadow，保持文字稳定 */
        @keyframes message-bubble-breathe {
            0%, 100% {
                box-shadow:
                    inset 0 2px 4px rgba(0, 0, 0, 0.08),
                    0 0 0 1px rgba(0, 0, 0, 0.12),
                    0 4px 12px rgba(0, 0, 0, 0.08),
                    0 0 20px rgba(0, 0, 0, 0.02);
            }
            50% {
                box-shadow:
                    inset 0 2px 4px rgba(0, 0, 0, 0.06),
                    0 0 0 1px rgba(0, 0, 0, 0.10),
                    0 6px 16px rgba(0, 0, 0, 0.06),
                    0 0 24px rgba(0, 0, 0, 0.03);
            }
        }

        /* 黑夜主题下的消息气泡选中状态优化 - 保持视觉稳定性 */
        body.dark-theme .message-bubble.actions-visible {
            box-shadow:
                inset 0 2px 4px rgba(255, 255, 255, 0.08),
                0 0 0 1px rgba(255, 255, 255, 0.12),
                0 4px 12px rgba(255, 255, 255, 0.08),
                0 0 20px rgba(255, 255, 255, 0.02);
            /* 与亮色主题保持一致的优化 */
            margin-top: -2px;
            margin-bottom: 2px;
            will-change: box-shadow;
            transform-origin: center center;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            backface-visibility: hidden;
            animation: message-bubble-breathe-dark 2s ease-in-out infinite;
        }

        /* 黑夜主题下的优化呼吸感动画 - 只变化 box-shadow */
        @keyframes message-bubble-breathe-dark {
            0%, 100% {
                box-shadow:
                    inset 0 2px 4px rgba(255, 255, 255, 0.08),
                    0 0 0 1px rgba(255, 255, 255, 0.12),
                    0 4px 12px rgba(255, 255, 255, 0.08),
                    0 0 20px rgba(255, 255, 255, 0.02);
            }
            50% {
                box-shadow:
                    inset 0 2px 4px rgba(255, 255, 255, 0.06),
                    0 0 0 1px rgba(255, 255, 255, 0.10),
                    0 6px 16px rgba(255, 255, 255, 0.06),
                    0 0 24px rgba(255, 255, 255, 0.03);
            }
        }

        /* 移动端点击显示操作按钮 */
        @media (hover: none) and (pointer: coarse) {
            .message-bubble.actions-visible .message-actions {
                max-height: 60px;
                opacity: 1;
                margin-top: var(--spacing-xs);
            }
        }
        .tts-play-button { font-size: 0.8em; }
        .tts-play-button i.fa-play::before { content: "\f04b"; }
        .tts-play-button i.fa-pause::before { content: "\f04c"; }
        .tts-play-button i.fa-spinner::before { content: "\f110"; animation: fa-spin 2s infinite linear; }
        .message-content mark { background-color: #ffe066; padding: 0.1em; border-radius: 2px; box-shadow: 0 0 0 1px #ffe066; }
        .message-bubble.search-highlight { outline: 2px solid #fd7e14; outline-offset: 2px; border-radius: 20px; }

        /* === 统一代码块样式系统 === */

        /* 所有代码块的基础样式 - 统一设计 */
        pre:not(.mermaid-code-display pre):not(.svg-code-view pre):not(.mermaid-code-view pre),
        .mermaid-code-view,
        .svg-code-view,
        pre[data-streaming-graphic="true"],
        pre[data-pending-graphic="true"] {
            max-width: 100%;
            position: relative;
            background: linear-gradient(135deg, #1a1e23 0%, #252a31 100%);
            color: #e6edf3;
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.08);
            margin: var(--spacing-lg) 0;
            font-size: 0.95em;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            box-shadow:
                0 4px 16px rgba(0, 0, 0, 0.12),
                0 1px 3px rgba(0, 0, 0, 0.08),
                inset 0 1px 0 rgba(255, 255, 255, 0.05);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* 悬停效果 */
        pre:not(.mermaid-code-display pre):not(.svg-code-view pre):not(.mermaid-code-view pre):hover,
        .mermaid-code-view:hover,
        .svg-code-view:hover {
            border-color: rgba(255, 255, 255, 0.12);
            box-shadow:
                0 8px 24px rgba(0, 0, 0, 0.16),
                0 2px 6px rgba(0, 0, 0, 0.12),
                inset 0 1px 0 rgba(255, 255, 255, 0.08);
            transform: translateY(-1px);
        }

        /* 统一代码块头部样式 */
        .code-block-header,
        .mermaid-code-header,
        .svg-code-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.08) 0%,
                rgba(255, 255, 255, 0.04) 50%,
                rgba(255, 255, 255, 0.02) 100%);
            padding: 8px 16px;
            font-size: 0.9em;
            color: #f0f6fc;
            border-bottom: 1px solid rgba(255, 255, 255, 0.08);
            border-radius: 12px 12px 0 0;
            flex-shrink: 0;
            backdrop-filter: blur(8px);
            /* 字体渲染优化 */
            font-family: var(--font-family-base);
            font-weight: 500;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: var(--text-rendering-crisp);
            font-feature-settings: "liga" 1, "kern" 1, "tnum" 1;
            letter-spacing: 0.01em;
            position: relative;
        }

        /* 头部微光效果 */
        .code-block-header::before,
        .mermaid-code-header::before,
        .svg-code-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg,
                transparent 0%,
                rgba(255, 255, 255, 0.2) 50%,
                transparent 100%);
            opacity: 0.6;
        }

        /* 统一语言标签样式 */
        .language-tag {
            display: inline-flex;
            align-items: center;
            text-transform: uppercase;
            font-weight: 600;
            font-size: 0.8em;
            letter-spacing: 0.8px;
            color: #ffffff;
            background: linear-gradient(135deg,
                rgba(100, 181, 246, 0.2) 0%,
                rgba(100, 181, 246, 0.1) 100%);
            border: 1px solid rgba(100, 181, 246, 0.3);
            border-radius: 6px;
            padding: 4px 8px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            /* 字体渲染优化 */
            font-family: var(--font-family-base);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: var(--text-rendering-crisp);
            font-feature-settings: "liga" 0, "kern" 1, "tnum" 1;
            font-variant-numeric: tabular-nums;
            transition: all 0.2s ease;
        }

        /* 不同语言的颜色主题 */
        .language-tag[data-language="javascript"] {
            background: linear-gradient(135deg, rgba(247, 223, 30, 0.2) 0%, rgba(247, 223, 30, 0.1) 100%);
            border-color: rgba(247, 223, 30, 0.4);
        }
        .language-tag[data-language="python"] {
            background: linear-gradient(135deg, rgba(55, 118, 171, 0.2) 0%, rgba(55, 118, 171, 0.1) 100%);
            border-color: rgba(55, 118, 171, 0.4);
        }
        .language-tag[data-language="mermaid"] {
            background: linear-gradient(135deg, rgba(255, 143, 0, 0.2) 0%, rgba(255, 143, 0, 0.1) 100%);
            border-color: rgba(255, 143, 0, 0.4);
        }
        .language-tag[data-language="svg"] {
            background: linear-gradient(135deg, rgba(255, 87, 34, 0.2) 0%, rgba(255, 87, 34, 0.1) 100%);
            border-color: rgba(255, 87, 34, 0.4);
        }

        .code-block-header-buttons {
            display: flex;
            gap: 6px;
            align-items: center;
        }

        /* 统一代码块操作按钮样式 */
        .code-action-button,
        .code-footer-button {
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.08) 0%,
                rgba(255, 255, 255, 0.04) 100%);
            border: 1px solid rgba(255, 255, 255, 0.12);
            color: #e6edf3;
            padding: 6px 12px;
            cursor: pointer;
            border-radius: 8px;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            display: inline-flex;
            align-items: center;
            gap: 6px;
            font-size: 0.85em;
            font-weight: 500;
            user-select: none;
            -webkit-user-select: none;
            touch-action: manipulation;
            backdrop-filter: blur(8px);
            position: relative;
            overflow: hidden;
            /* 字体渲染优化 */
            font-family: var(--font-family-base);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: var(--text-rendering-crisp);
            font-feature-settings: "liga" 1, "kern" 1;
            letter-spacing: 0.01em;
            line-height: 1.2;
        }

        /* 按钮微光效果 */
        .code-action-button::before,
        .code-footer-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                transparent 0%,
                rgba(255, 255, 255, 0.1) 50%,
                transparent 100%);
            transition: left 0.5s ease;
        }

        /* 按钮交互状态 */
        .code-action-button:hover,
        .code-footer-button:hover {
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.15) 0%,
                rgba(255, 255, 255, 0.08) 100%);
            border-color: rgba(255, 255, 255, 0.2);
            color: #ffffff;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            /* 悬停时保持字体清晰 */
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .code-action-button:hover::before,
        .code-footer-button:hover::before {
            left: 100%;
        }

        .code-action-button:active,
        .code-footer-button:active {
            transform: translateY(0);
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.12) 0%,
                rgba(255, 255, 255, 0.06) 100%);
        }

        /* 成功状态 */
        .code-action-button.success {
            background: linear-gradient(135deg,
                rgba(76, 175, 80, 0.2) 0%,
                rgba(76, 175, 80, 0.1) 100%);
            border-color: rgba(76, 175, 80, 0.4);
            color: #4caf50;
        }

        .code-action-button.success .fa-check { color: #4caf50; }

        .code-scroll-wrapper {
            flex-grow: 1;
            overflow: auto;
            padding: 1em 1.2em;
            background: #1a1a1a;
            position: relative;
        }

        pre code.hljs {
            display: block;
            background: none;
            color: inherit;
            line-height: 1.6;
            user-select: text;
            -webkit-user-select: text;
            padding: 0;
            min-width: 100%;
            box-sizing: border-box;
            width: max-content;
            overflow-wrap: normal;
            white-space: pre;
            font-family: 'JetBrains Mono', 'Fira Code', 'Cascadia Code', Consolas, Monaco, 'Andale Mono', monospace;
        }

        pre code.hljs *, pre code.hljs *::before, pre code.hljs *::after {
            margin: 0;
            padding: 0;
            border: 0;
            background: none;
            box-shadow: none;
            font-style: normal;
            font-weight: normal;
            text-decoration: none;
            display: inline;
            white-space: pre;
        }

        /* 统一代码块底部样式 */
        .code-block-footer,
        .mermaid-code-footer,
        .svg-code-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.04) 0%,
                rgba(255, 255, 255, 0.02) 50%,
                rgba(255, 255, 255, 0.01) 100%);
            padding: 6px 16px;
            min-height: 40px; /* 与小巧折叠按钮高度(32px) + padding相适应 */
            border-top: 1px solid rgba(255, 255, 255, 0.06);
            border-radius: 0 0 12px 12px;
            gap: var(--spacing-sm);
            flex-shrink: 0;
            position: relative;
            backdrop-filter: blur(8px);
            /* 字体渲染优化 */
            font-family: var(--font-family-base);
            font-weight: 400;
            font-size: 0.8em;
            color: #8b949e;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: var(--text-rendering-crisp);
            font-feature-settings: "liga" 1, "kern" 1;
            letter-spacing: 0.01em;
        }

        /* 代码块底栏按钮容器 */
        .code-block-footer .footer-buttons-left,
        .code-block-footer .footer-buttons-right,
        .mermaid-code-footer .footer-buttons-left,
        .mermaid-code-footer .footer-buttons-right,
        .svg-code-footer .footer-buttons-left,
        .svg-code-footer .footer-buttons-right {
            display: flex;
            gap: var(--spacing-sm);
            align-items: center;
        }

        /* 右侧按钮容器特殊样式 */
        .code-block-footer .footer-buttons-right,
        .mermaid-code-footer .footer-buttons-right,
        .svg-code-footer .footer-buttons-right {
            justify-content: flex-end;
        }

        /* 现代化折叠按钮容器 */
        .code-block-footer .footer-center,
        .mermaid-code-footer .footer-center,
        .svg-code-footer .footer-center {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            align-items: center;
        }

        /* === 新的渐进式代码块折叠系统 === */

        /* 简约清新折叠按钮 - 与底栏自然融合 */
        .collapse-button {
            /* 与底栏保持一致的轻量背景 */
            background: rgba(255, 255, 255, 0.03);
            border: 0.5px solid rgba(255, 255, 255, 0.06);
            color: rgba(255, 255, 255, 0.65);
            padding: 3px 5px;
            cursor: pointer;
            border-radius: 10px;
            /* 垂直布局：图标在上，行数在下 */
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 1px;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            font-size: 0.8rem;
            font-weight: 400;
            /* 小巧精致尺寸 - 更加紧凑 */
            min-width: 38px;
            height: 32px;
            /* 移除阴影，更加融合 */
            box-shadow: none;
            position: relative;
            overflow: hidden;
            user-select: none;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", sans-serif;
            letter-spacing: 0.005em;
            line-height: 1;
            /* 与底栏一致的模糊效果 */
            backdrop-filter: blur(8px);
            /* 减少视觉重量 */
            opacity: 0.9;
        }

        /* 精致悬停效果 */
        .collapse-button:hover {
            background: rgba(255, 255, 255, 0.08);
            border-color: rgba(255, 255, 255, 0.12);
            color: rgba(255, 255, 255, 0.8);
            /* 移除位移，保持稳定 */
            transform: none;
            /* 微妙的阴影提示 */
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            opacity: 1;
        }

        /* 精致激活效果 */
        .collapse-button:active {
            background: rgba(255, 255, 255, 0.05);
            border-color: rgba(255, 255, 255, 0.08);
            color: rgba(255, 255, 255, 0.7);
            transform: scale(0.96);
            box-shadow: none;
        }

        /* 折叠按钮焦点效果 - 仅键盘导航显示 */
        .collapse-button:focus {
            outline: none;
        }

        .collapse-button:focus-visible {
            outline: none;
            border-color: var(--focus-border-color);
            box-shadow: 0 0 0 var(--focus-glow-radius) var(--focus-glow-color);
            transition: var(--focus-transition);
        }

        /* 垂直布局的图标样式 */
        .collapse-button .collapse-icon {
            transition: transform 0.2s ease;
            font-size: 0.9em;
            opacity: 0.8;
            margin-bottom: 1px;
        }

        /* 垂直布局的行数显示样式 */
        .collapse-button .collapse-line-count {
            font-size: 0.75em;
            font-weight: 500;
            opacity: 0.9;
            white-space: nowrap;
            line-height: 1;
            /* 数字字体优化 */
            font-variant-numeric: tabular-nums;
            letter-spacing: 0.02em;
        }

        /* 悬停时图标和行数更清晰 */
        .collapse-button:hover .collapse-icon,
        .collapse-button:hover .collapse-line-count {
            opacity: 1;
        }

        /* 折叠状态下的图标样式变化 */
        .collapse-button.collapsed .collapse-icon.fa-chevron-up {
            transform: rotate(180deg);
        }

        /* 代码预览区域 */
        .code-preview-container {
            position: relative;
            overflow: hidden;
            transition: max-height 0.3s ease;
            /* 与原始代码块保持相同的样式 */
            flex-grow: 1;
            padding: 1em 1.2em;
            background: #1a1a1a;
        }

        /* 简约渐变省略提示 */
        .code-preview-fade {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 45px;
            background: linear-gradient(to bottom,
                rgba(26, 26, 26, 0) 0%,
                rgba(26, 26, 26, 0.6) 50%,
                rgba(26, 26, 26, 0.9) 80%,
                rgba(26, 26, 26, 1) 100%);
            pointer-events: auto;
            display: flex;
            align-items: flex-end;
            justify-content: center;
            padding-bottom: 10px;
            cursor: pointer;
            transition: opacity 0.2s ease;
        }

        .code-preview-fade:hover {
            opacity: 0.8;
        }

        /* 省略号指示器 */
        .code-preview-fade::after {
            content: '⋯ 点击展开更多';
            color: rgba(139, 148, 158, 0.8);
            font-size: 0.8em;
            font-weight: 500;
            letter-spacing: 1px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        /* 隐藏的代码部分 */
        .code-hidden-content {
            display: none;
            animation: fadeIn 0.3s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .code-footer-button {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.85em;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 6px;
            user-select: none;
            -webkit-user-select: none;
            touch-action: manipulation;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
        }

        .code-footer-button:hover {
            opacity: 0.9;
        }

        .code-footer-button.run-button {
            background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
            box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
        }
        .code-footer-button.run-button:hover {
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);
        }

        .code-footer-button.graphic-button {
            background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
            box-shadow: 0 2px 4px rgba(255, 152, 0, 0.3);
        }
        .code-footer-button.graphic-button:hover {
            box-shadow: 0 4px 12px rgba(255, 152, 0, 0.4);
        }

        /* 流式图形代码块精致样式 - 去除黄色竖条 */
        pre[data-streaming-graphic="true"] {
            background: linear-gradient(135deg, #1a1a1a 0%, #252525 100%);
            position: relative;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        pre[data-streaming-graphic="true"]:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        pre[data-streaming-graphic="true"] .language-tag {
            color: #f0f0f0;
            text-shadow: none;
        }

        /* 流式代码块底部栏高度调整 - 与小巧折叠按钮高度相适应 */
        pre[data-streaming-graphic="true"] .code-block-footer {
            min-height: 40px; /* 与小巧折叠按钮高度(32px) + padding相适应 */
            padding: 6px 16px;
        }

        pre[data-streaming-graphic="true"] .language-tag::after {
            content: " • 流式输出";
            color: #b0b8c9;
            font-size: 0.75em;
            font-weight: 400;
            margin-left: 6px;
            opacity: 0.8;
            transition: opacity 0.3s ease, color 0.3s ease;
        }

        /* 已即时渲染但保持流式外观的状态 - 只在非流式状态下显示 */
        pre[data-instant-rendered="true"]:not([data-streaming-graphic="true"]) .language-tag::after {
            content: " • 已渲染";
            color: #4caf50;
            opacity: 0.9;
        }

        /* 流式图形代码块按钮优化 */
        pre[data-streaming-graphic="true"] .code-action-button.graphic-preview {
            background: linear-gradient(135deg, rgba(255, 152, 0, 0.2) 0%, rgba(245, 124, 0, 0.2) 100%);
            border-color: rgba(255, 152, 0, 0.4);
            color: #ffb74d;
            transition: background 0.3s ease, border-color 0.3s ease, color 0.3s ease;
        }

        /* 已渲染状态的按钮样式 - 只在非流式状态下应用 */
        pre[data-instant-rendered="true"]:not([data-streaming-graphic="true"]) .code-action-button.graphic-preview {
            background: linear-gradient(135deg, rgba(76, 175, 80, 0.2) 0%, rgba(56, 142, 60, 0.2) 100%);
            border-color: rgba(76, 175, 80, 0.4);
            color: #81c784;
        }

        pre[data-streaming-graphic="true"] .code-action-button.graphic-preview:hover {
            background: linear-gradient(135deg, rgba(255, 152, 0, 0.3) 0%, rgba(245, 124, 0, 0.3) 100%);
            border-color: rgba(255, 152, 0, 0.6);
            color: #ff9800;
            box-shadow: 0 2px 12px rgba(255, 152, 0, 0.3);
        }

        /* 黄色竖条已移除 - 流式状态通过文字标签指示 */

        /* 流式代码块黄色竖条动画已移除 */

        /* --- SVG Rendering Container --- */
        /* SVG Container (Aligned with Mermaid) */
        .svg-container { position: relative; margin: 0.8em 0; }
        .svg-diagram-view { text-align: center; background-color: #fff; padding: 10px; border-radius: 6px; border: 1px solid #eee; min-height: 45px; display: block; overflow: auto; transition: background-color 0.3s, border-color 0.3s; box-shadow: 0 1px 3px rgba(0,0,0,0.05); }
        .svg-diagram-view.hidden { display: none; }
        .svg-diagram-view svg { max-width: 100%; height: auto; display: block; margin: 0 auto; }

        /* SVG Actions (Aligned with Mermaid) */
        .svg-actions { position: absolute; top: 8px; right: 8px; display: flex; gap: 6px; opacity: 0; pointer-events: none; z-index: 20; }
        .svg-container:hover .svg-actions, .svg-container.actions-visible .svg-actions { opacity: 1; pointer-events: auto; }
        .svg-container.code-view-active .svg-actions { display: none !important; }

        /* === 玻璃拟态风格 (默认) === */
        .svg-action-button {
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.25) 0%,
                rgba(255, 255, 255, 0.1) 100%);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            cursor: pointer;
            color: #374151;
            font-size: 0.75em;
            font-weight: 500;
            padding: 6px 10px;
            line-height: 1.2;
            backdrop-filter: blur(12px);
            box-shadow:
                0 2px 8px rgba(0, 0, 0, 0.08),
                0 1px 2px rgba(0, 0, 0, 0.04),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            gap: 4px;
            min-width: 32px;
            justify-content: center;
        }

        .svg-action-button:hover {
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.35) 0%,
                rgba(255, 255, 255, 0.15) 100%);
            border-color: rgba(255, 255, 255, 0.3);
            color: #2563eb;
            /* 移除动效：transform: translateY(-2px) scale(1.02); */
            box-shadow:
                0 4px 16px rgba(0, 0, 0, 0.12),
                0 2px 4px rgba(0, 0, 0, 0.06),
                inset 0 1px 0 rgba(255, 255, 255, 0.4);
        }

        .svg-action-button.active {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            border-color: #2563eb;
            color: white;
            box-shadow:
                0 4px 16px rgba(59, 130, 246, 0.3),
                0 2px 4px rgba(59, 130, 246, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        /* SVG代码视图 - 与统一代码块样式保持一致 */
        .svg-code-view {
            display: none; /* 默认隐藏，只有在.visible时显示 */
            background: linear-gradient(135deg, #1a1e23 0%, #252a31 100%);
            color: #e6edf3;
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.08);
            margin: var(--spacing-lg) 0;
            overflow: hidden;
            box-shadow:
                0 4px 16px rgba(0, 0, 0, 0.12),
                0 1px 3px rgba(0, 0, 0, 0.08),
                inset 0 1px 0 rgba(255, 255, 255, 0.05);
            flex-direction: column;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        .svg-code-view.visible {
            display: flex; /* 使用flex布局与统一样式保持一致 */
        }

        .svg-code-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.08) 100%);
            padding: 8px 14px;
            font-size: 0.85em;
            color: #e0e0e0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.15);
            flex-shrink: 0;
            backdrop-filter: blur(10px);
        }

        .svg-code-header .language-label {
            text-transform: uppercase;
            font-weight: 600;
            font-size: 0.85em;
            letter-spacing: 0.5px;
            color: #fff;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        .svg-code-actions {
            display: flex;
            gap: 6px;
            align-items: center;
        }

        .svg-code-actions button {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: #e0e0e0;
            padding: 4px 8px;
            cursor: pointer;
            border-radius: 6px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 4px;
            font-size: 0.85em;
            font-weight: 500;
            user-select: none;
            -webkit-user-select: none;
            touch-action: manipulation;
            backdrop-filter: blur(5px);
        }

        .svg-code-actions button:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.3);
            color: #fff;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }

        .svg-code-actions button .fa-check { color: #4caf50; }

        .svg-code-footer {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.05) 100%);
            padding: 8px 14px;
            border-top: 1px solid rgba(255, 255, 255, 0.12);
            gap: 8px;
            flex-shrink: 0;
            backdrop-filter: blur(10px);
        }

        .svg-code-footer button {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.85em;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 6px;
            user-select: none;
            -webkit-user-select: none;
            touch-action: manipulation;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
        }

        .svg-code-footer button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
        }

        /* 高优先级选择器确保SVG代码块pre元素无缝融合 */
        .svg-code-view pre,
        .svg-container .svg-code-view pre {
            margin: 0 !important;
            padding: 1em 1.2em;
            white-space: pre-wrap;
            word-break: break-all;
            font-family: 'JetBrains Mono', 'Fira Code', 'Cascadia Code', Consolas, Monaco, 'Andale Mono', monospace;
            font-size: 0.95em;
            line-height: 1.6;
            max-height: 400px;
            overflow-y: auto;
            background: transparent !important; /* 强制移除独立背景，使用容器背景 */
            border-radius: 0 !important; /* 强制移除圆角，与容器无缝融合 */
            border: none !important; /* 移除可能的边框 */
            box-shadow: none !important; /* 移除可能的阴影 */
        }

        .svg-code-view pre code {
            display: block;
            background: none;
            color: inherit;
            line-height: 1.6;
            user-select: text;
            -webkit-user-select: text;
            padding: 0;
            min-width: 100%;
            box-sizing: border-box;
            width: max-content;
            overflow-wrap: normal;
            white-space: pre;
        }

        /* --- Mermaid Diagram Container --- */
        /* --- Mermaid Diagram Container (Enhanced & Refined) --- */
        .mermaid-container { position: relative; margin: 0.8em 0; }
        .mermaid-diagram-view { text-align: center; background-color: #fff; padding: 10px; border-radius: 6px; border: 1px solid #eee; min-height: 45px; display: block; overflow: auto; transition: background-color 0.3s, border-color 0.3s; box-shadow: 0 1px 3px rgba(0,0,0,0.05); }
        .mermaid-diagram-view.hidden { display: none; }
        .mermaid-diagram-view svg { max-width: 100%; height: auto; display: block; margin: 0 auto; }

        /* Actions on the diagram (like 'View Code') - Refined */
        .mermaid-actions { position: absolute; top: 8px; right: 8px; display: flex; gap: 6px; opacity: 0; pointer-events: none; z-index: 20; }
        .mermaid-container:hover .mermaid-actions, .mermaid-container.actions-visible .mermaid-actions { opacity: 1; pointer-events: auto; }
        .mermaid-container.code-view-active .mermaid-actions { display: none !important; } /* Hide when code view is active */

        /* === 玻璃拟态风格 (默认) === */
        .mermaid-action-button {
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.25) 0%,
                rgba(255, 255, 255, 0.1) 100%);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            cursor: pointer;
            color: #374151;
            font-size: 0.75em;
            font-weight: 500;
            padding: 6px 10px;
            line-height: 1.2;
            backdrop-filter: blur(12px);
            box-shadow:
                0 2px 8px rgba(0, 0, 0, 0.08),
                0 1px 2px rgba(0, 0, 0, 0.04),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            gap: 4px;
            min-width: 32px;
            justify-content: center;
        }

        .mermaid-action-button:hover {
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.35) 0%,
                rgba(255, 255, 255, 0.15) 100%);
            border-color: rgba(255, 255, 255, 0.3);
            color: #2563eb;
            /* 移除动效：transform: translateY(-2px) scale(1.02); */
            box-shadow:
                0 4px 16px rgba(0, 0, 0, 0.12),
                0 2px 4px rgba(0, 0, 0, 0.06),
                inset 0 1px 0 rgba(255, 255, 255, 0.4);
        }

        .mermaid-action-button.active {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            border-color: #2563eb;
            color: white;
            box-shadow:
                0 4px 16px rgba(59, 130, 246, 0.3),
                0 2px 4px rgba(59, 130, 246, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        /* === 黏土拟态风格 (Clay Morphism) === */
        .clay-style .svg-action-button,
        .clay-style .mermaid-action-button {
            background: linear-gradient(145deg, #f8fafc 0%, #e2e8f0 100%);
            border: 2px solid #cbd5e1;
            border-radius: 16px;
            color: #475569;
            font-weight: 600;
            padding: 8px 12px;
            box-shadow:
                6px 6px 12px rgba(148, 163, 184, 0.4),
                -6px -6px 12px rgba(255, 255, 255, 0.8),
                inset 2px 2px 4px rgba(255, 255, 255, 0.6),
                inset -2px -2px 4px rgba(148, 163, 184, 0.2);
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .clay-style .svg-action-button:hover,
        .clay-style .mermaid-action-button:hover {
            background: linear-gradient(145deg, #ffffff 0%, #f1f5f9 100%);
            border-color: #94a3b8;
            color: #1e40af;
            /* 移除动效：transform: translateY(-1px); */
            box-shadow:
                8px 8px 16px rgba(148, 163, 184, 0.5),
                -8px -8px 16px rgba(255, 255, 255, 0.9),
                inset 3px 3px 6px rgba(255, 255, 255, 0.7),
                inset -3px -3px 6px rgba(148, 163, 184, 0.3);
        }

        .clay-style .svg-action-button.active,
        .clay-style .mermaid-action-button.active {
            background: linear-gradient(145deg, #3b82f6 0%, #1d4ed8 100%);
            border-color: #1e40af;
            color: white;
            box-shadow:
                inset 4px 4px 8px rgba(29, 78, 216, 0.6),
                inset -4px -4px 8px rgba(96, 165, 250, 0.4),
                2px 2px 8px rgba(59, 130, 246, 0.3);
        }

        /* === 新粗犷主义风格 (Neo-Brutalism) === */
        .brutalist-style .svg-action-button,
        .brutalist-style .mermaid-action-button {
            background: #ffffff;
            border: 3px solid #000000;
            border-radius: 0;
            color: #000000;
            font-weight: 700;
            font-family: 'Courier New', monospace;
            padding: 8px 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: 4px 4px 0 #000000;
            transition: all 0.15s ease;
            position: relative;
        }

        .brutalist-style .svg-action-button:hover,
        .brutalist-style .mermaid-action-button:hover {
            background: #ffff00;
            color: #000000;
            /* 移除动效：transform: translate(-2px, -2px); */
            box-shadow: 6px 6px 0 #000000;
        }

        .brutalist-style .svg-action-button.active,
        .brutalist-style .mermaid-action-button.active {
            background: #ff0000;
            color: #ffffff;
            transform: translate(2px, 2px);
            box-shadow: 2px 2px 0 #000000;
        }

        /* Mermaid代码视图 - 与统一代码块样式保持一致 */
        .mermaid-code-view {
            display: none; /* 默认隐藏，只有在.visible时显示 */
            background: linear-gradient(135deg, #1a1e23 0%, #252a31 100%);
            color: #e6edf3;
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.08);
            margin: var(--spacing-lg) 0;
            overflow: hidden;
            box-shadow:
                0 4px 16px rgba(0, 0, 0, 0.12),
                0 1px 3px rgba(0, 0, 0, 0.08),
                inset 0 1px 0 rgba(255, 255, 255, 0.05);
            flex-direction: column;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        .mermaid-code-view.visible {
            display: flex; /* 使用flex布局与统一样式保持一致 */
        }

        .mermaid-code-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.08) 100%);
            padding: 8px 14px;
            font-size: 0.85em;
            color: #e0e0e0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.15);
            flex-shrink: 0;
            backdrop-filter: blur(10px);
        }

        .mermaid-code-header .language-label {
            text-transform: uppercase;
            font-weight: 600;
            font-size: 0.85em;
            letter-spacing: 0.5px;
            color: #fff;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        .mermaid-code-actions {
            display: flex;
            gap: 6px;
            align-items: center;
        }

        .mermaid-code-actions button {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: #e0e0e0;
            padding: 4px 8px;
            cursor: pointer;
            border-radius: 6px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 4px;
            font-size: 0.85em;
            font-weight: 500;
            user-select: none;
            -webkit-user-select: none;
            touch-action: manipulation;
            backdrop-filter: blur(5px);
        }

        .mermaid-code-actions button:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.3);
            color: #fff;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }

        .mermaid-code-actions button .fa-check { color: #4caf50; }

        /* 高优先级选择器确保Mermaid代码块pre元素无缝融合 */
        .mermaid-code-view pre,
        .mermaid-container .mermaid-code-view pre {
            margin: 0 !important;
            padding: 1em 1.2em;
            white-space: pre-wrap;
            word-break: break-all;
            font-family: 'JetBrains Mono', 'Fira Code', 'Cascadia Code', Consolas, Monaco, 'Andale Mono', monospace;
            font-size: 0.95em;
            line-height: 1.6;
            max-height: 400px;
            overflow-y: auto;
            background: transparent !important; /* 强制移除独立背景，使用容器背景 */
            border-radius: 0 !important; /* 强制移除圆角，与容器无缝融合 */
            border: none !important; /* 移除可能的边框 */
            box-shadow: none !important; /* 移除可能的阴影 */
        }

        .mermaid-code-footer {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.05) 100%);
            padding: 8px 14px;
            border-top: 1px solid rgba(255, 255, 255, 0.12);
            gap: 8px;
            flex-shrink: 0;
            backdrop-filter: blur(10px);
        }

        .mermaid-code-footer button {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.85em;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 6px;
            user-select: none;
            -webkit-user-select: none;
            touch-action: manipulation;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
        }

        .mermaid-code-footer button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
        }
        .mermaid-loading, .mermaid-placeholder { min-height: 45px; background-color: #f8f9fa; border: 1px dashed #ccc; border-radius: 6px; display: flex; align-items: center; justify-content: center; color: #6c757d; font-style: italic; margin: 0.8em 0; padding: 10px; box-sizing: border-box; text-align: center; box-shadow: 0 1px 3px rgba(0,0,0,0.03); font-size: 0.85em; }
        .mermaid.mermaid-error { background-color: #fff3f3; border-color: #f5c6cb; padding: 10px; text-align: left; color:#dc3545; border-radius: 6px; box-shadow: 0 1px 4px rgba(0,0,0,0.05); font-size: 0.85em; }
        .mermaid-error-message { font-weight: bold; margin-bottom: 6px; display: block; font-size: 0.85em; }
        .mermaid-error .mermaid-code-display pre { background-color: #f8f9fa; border: 1px dashed #ced4da; padding: 8px; border-radius: 4px; text-align: left; white-space: pre-wrap; word-break: break-all; font-size: 0.8em; color: #333; max-height: 180px; overflow-y: auto; margin-top: 6px; }

        /* --- Modals (Base Styles) --- */
        .modal { display: none; position: fixed; z-index: 1050; left: 0; top: 0; width: 100%; height: 100%; overflow: hidden; background-color: rgba(0,0,0,0.5); opacity: 0; transition: opacity 0.2s ease; }
        .modal.visible { display: flex; align-items: center; justify-content: center; opacity: 1; }
        .modal-content { background-color: var(--bg-surface); padding: 20px 25px 25px 25px; border: 1px solid var(--border-primary); width: 90%; border-radius: 10px; box-shadow: var(--shadow-xl); position: relative; transform: scale(0.95); transition: transform 0.2s ease; margin: auto; max-height: 85vh; display: flex; flex-direction: column; }
        .modal.visible .modal-content { transform: scale(1); }
        .modal-close-button { color: #6c757d; position: absolute; top: 12px; right: 16px; font-size: 24px; font-weight: bold; cursor: pointer; line-height: 1; opacity: 0.7; z-index: 10; }
        .modal-close-button:hover { color: #343a40; opacity: 1; }
        .modal h2 { margin-top: 0; color: #343a40; text-align: center; margin-bottom: 25px; font-weight: 500; font-size: 1.2em; flex-shrink: 0; }
        .modal-form-area { overflow-y: auto; overflow-x: hidden; /* V48.1: Prevent horizontal scroll */ padding-right: 5px; /* Reduce padding slightly */ margin-bottom: 15px; flex-grow: 1; padding-left: 5px; }

        .form-group { margin-bottom: 18px; position: relative; }
        .form-group label { display: block; margin-bottom: 6px; font-weight: 500; color: #495057; font-size: 0.9em; }
        .form-group input[type="text"], .form-group input[type="password"], .form-group input[type="number"], .form-group textarea, .form-group input[type="file"] {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid var(--border-secondary);
            border-radius: 8px;
            box-sizing: border-box;
            font-size: 0.95em;
            background: var(--bg-surface);
            height: 42px;
            transition: all 0.2s ease;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }
        .form-group input[type="file"] { padding: 10px 16px; height: auto; }
        .form-group textarea {
            height: auto;
            min-height: 90px;
            resize: vertical;
            line-height: 1.5;
            overflow-y: auto;
            padding: 14px 16px;
        }
        /* .form-group input:focus, .form-group textarea:focus 样式已统一到全局聚焦效果系统中 */
        .form-group input.input-invalid, .form-group textarea.input-invalid, .form-group .custom-selector-trigger.input-invalid { border-color: #dc3545 !important; }
        .invalid-feedback { display: none; width: 100%; margin-top: .25rem; font-size: .8em; color: #dc3545; }
        .form-group.invalid .invalid-feedback { display: block; }
        #settings-modal #profile-name-input { width: 50%; }
        .input-with-button { position: relative; display: flex; }
        .input-with-button input { flex-grow: 1; padding-right: 45px !important; font-family: monospace; height: 40px; box-sizing: border-box; border-top-right-radius: 0; border-bottom-right-radius: 0; border-right: none; }
        .toggle-visibility-button { position: absolute; right: 0; top: 0; bottom: 0; height: 100%; width: 40px; background: #fff; border: 1px solid #ced4da; border-left: none; padding: 0; margin: 0; cursor: pointer; color: #6c757d; font-size: 1em; display: flex; align-items: center; justify-content: center; opacity: 0.7; z-index: 2; border-top-right-radius: 6px; border-bottom-right-radius: 6px; box-sizing: border-box; }
        .toggle-visibility-button:hover { opacity: 1; color: #343a40; background-color: #f8f9fa; }
        .input-with-button input:focus + .toggle-visibility-button { border-color: #ced4da; border-left: none; z-index: 3; }
        .input-with-button input.input-invalid + .toggle-visibility-button { border-color: #dc3545 !important; }

        /* --- Settings Modal Specific --- */
        #settings-modal .modal-content { max-width: 480px; margin: auto; }
        .modal-profile-selector { display: flex; gap: 10px; margin-bottom: 20px; align-items: center; border-bottom: 1px solid var(--border-primary); padding-bottom: 15px; flex-shrink: 0; }
        .modal-profile-selector label { flex-shrink: 0; margin-bottom: 0; font-weight: 500; line-height: 38px; }
        #settings-profile-selector-container { flex-grow: 1; min-width: 180px; }
        .model-selection-group-container { position: relative; margin-bottom: 18px; }
        .model-selection-group-container label { font-weight: 500; font-size: 0.9em; margin-bottom: 6px; display: block; color: #495057; }
        .model-selection-group { display: flex; align-items: center; gap: 8px; position: relative; }
        .model-selection-group .custom-selector-container { flex-grow: 1; }
        .modal-actions { display: flex; flex-direction: column; align-items: center; margin-top: 25px; border-top: 1px solid #dee2e6; padding-top: 20px; gap: 15px; flex-shrink: 0; }
        .modal-action-row { display: flex; justify-content: center; flex-wrap: wrap; gap: 10px; width: 100%; }
        .modal-actions button {
            padding: 8px 16px;
            border: 0.5px solid transparent;
            border-radius: 6px;
            cursor: pointer;
            font-size: 13px;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            height: 36px;
            box-sizing: border-box;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            min-width: 80px;
            line-height: 20px;
        }
        .modal-actions button:disabled { background-color: #adb5bd; cursor: not-allowed; opacity: 0.6; }
        #save-changes-button {
            background-color: var(--color-primary);
            color: white;
            border-color: var(--color-primary);
        }
        #save-changes-button:hover:not(:disabled) {
            background-color: var(--color-primary-hover);
            border-color: var(--color-primary-hover);
            transform: translateY(-0.5px);
        }
        #delete-profile-button {
            background-color: var(--color-danger);
            color: white;
            border-color: var(--color-danger);
        }
        #delete-profile-button:hover:not(:disabled) {
            background-color: var(--color-danger-hover);
            border-color: var(--color-danger-hover);
            transform: translateY(-0.5px);
        }
        #set-default-button {
            background-color: var(--color-warning);
            color: white;
            border-color: var(--color-warning);
        }
        #set-default-button:hover:not(:disabled) {
            background-color: var(--color-warning-hover);
            border-color: var(--color-warning-hover);
            transform: translateY(-0.5px);
        }
        #set-default-button.is-default {
            background-color: var(--color-gray-500);
            color: white;
            cursor: default;
        }
        #set-default-button:disabled:not(.is-default) {
            background-color: var(--color-gray-400);
            color: white;
        }
        #cancel-settings-button {
            background-color: var(--color-gray-500);
            color: white;
            border-color: var(--color-gray-500);
        }
        #cancel-settings-button:hover {
            background-color: var(--color-gray-600);
            border-color: var(--color-gray-600);
            transform: translateY(-0.5px);
        }
        #save-as-template-button {
            background-color: var(--color-info);
            color: white;
            border-color: var(--color-info);
        }
        #save-as-template-button:hover:not(:disabled) {
            background-color: var(--color-info-hover);
            border-color: var(--color-info-hover);
            transform: translateY(-0.5px);
        }
        #manage-templates-button {
            background-color: #fd7e14;
            color: white;
            border-color: #fd7e14;
        }
        #manage-templates-button:hover {
            background-color: #e66a00;
            border-color: #e66a00;
            transform: translateY(-0.5px);
        }

        /* --- Manage Templates Modal Specific --- */
        #manage-templates-modal .modal-content { max-width: 480px; margin: auto; }
        #user-template-list { list-style: none; padding: 0; max-height: 45vh; overflow-y: auto; margin-bottom: 25px; border: 1px solid #eee; border-radius: 8px; }
        #user-template-list li { display: flex; justify-content: space-between; align-items: center; padding: 10px 15px; border-bottom: 1px solid #f0f0f0; }
        #user-template-list li:last-child { border-bottom: none; }
        #user-template-list span { flex-grow: 1; margin-right: 15px; font-size: 0.95em; }
        .delete-user-template-button { background: none; border: 1px solid #dc3545; color: #dc3545; border-radius: 6px; padding: 4px 8px; font-size: 0.85em; cursor: pointer; display: inline-flex; align-items: center; gap: 3px; }
        .delete-user-template-button:hover { background-color: #dc3545; color: white; }

        /* --- V48: Conversation Settings Modal --- */
        #conversation-settings-modal .modal-content { max-width: 500px; }
        #save-conversation-settings-button {
            background-color: var(--color-primary);
            color: white;
            border-color: var(--color-primary);
        }
        #save-conversation-settings-button:hover:not(:disabled) {
            background-color: var(--color-primary-hover);
            border-color: var(--color-primary-hover);
            transform: translateY(-0.5px);
        }
        #cancel-conversation-settings-button {
            background-color: var(--color-gray-500);
            color: white;
            border-color: var(--color-gray-500);
        }
        #cancel-conversation-settings-button:hover {
            background-color: var(--color-gray-600);
            border-color: var(--color-gray-600);
            transform: translateY(-0.5px);
        }
        #knowledge-base-file-input { width: 100%; margin-bottom: 10px; }
        #knowledge-base-file-list { list-style: none; padding: 0; max-height: 150px; overflow-y: auto; border: 1px solid #eee; border-radius: 4px; margin-top: 5px; }
        #knowledge-base-file-list li { display: flex; justify-content: space-between; align-items: center; padding: 5px 10px; border-bottom: 1px solid #f0f0f0; font-size: 0.9em; }
        #knowledge-base-file-list li:last-child { border-bottom: none; }
        #knowledge-base-file-list .kb-file-name { flex-grow: 1; margin-right: 10px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }
        #knowledge-base-file-list .kb-file-remove-button { background: none; border: none; color: #dc3545; cursor: pointer; font-size: 1em; padding: 0 5px; flex-shrink: 0; margin-left: auto; }
        #knowledge-base-file-list .kb-file-remove-button:hover { color: #c82333; }
        #knowledge-base-note { font-size: 0.85em; color: #6c757d; margin-top: 10px; }
        .tts-voice-selector-container label { font-weight: 500; font-size: 0.9em; margin-bottom: 6px; display: block; color: #495057; }

        /* --- Custom Confirm --- */
        #custom-confirm-overlay { display: none; position: fixed; inset: 0; background-color: rgba(0, 0, 0, 0.45); z-index: 1060; align-items: center; justify-content: center; opacity: 0; transition: opacity 0.2s ease; }
        #custom-confirm-overlay.visible { display: flex; opacity: 1; }
        #custom-confirm-box { background-color: #fff; padding: 20px 25px; border-radius: 10px; box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1); text-align: center; max-width: 300px; transform: scale(0.95); transition: transform 0.2s ease; }
        #custom-confirm-overlay.visible #custom-confirm-box { transform: scale(1); }
        #custom-confirm-message { margin-bottom: 20px; font-size: 1em; color: #495057; line-height: 1.6; white-space: normal; word-wrap: break-word; }
        #custom-confirm-buttons { display: flex; justify-content: center; gap: 12px; }
        .custom-confirm-button { padding: 7px 16px; border: none; border-radius: 6px; cursor: pointer; font-size: 0.9em; font-weight: 500; transition: background-color 0.2s ease, box-shadow 0.2s ease; }
        #custom-confirm-yes { background-color: #007bff; color: white; order: 1; }
        #custom-confirm-yes:hover { background-color: #0056b3; box-shadow: 0 2px 4px rgba(0, 123, 255, 0.2); }
        #custom-confirm-no { background-color: #6c757d; color: white; order: 0; }
        #custom-confirm-no:hover { background-color: #5a6268; box-shadow: 0 2px 4px rgba(108, 117, 125, 0.2); }
        #custom-confirm-box.danger #custom-confirm-yes { background-color: #dc3545; }
        #custom-confirm-box.danger #custom-confirm-yes:hover { background-color: #c82333; box-shadow: 0 2px 4px rgba(220, 53, 69, 0.2); }

        /* --- Custom Prompt --- */
        #custom-prompt-overlay { display: none; position: fixed; inset: 0; background-color: rgba(0, 0, 0, 0.45); z-index: 1060; align-items: center; justify-content: center; opacity: 0; transition: opacity 0.2s ease; }
        #custom-prompt-overlay.visible { display: flex; opacity: 1; }
        #custom-prompt-box { background-color: #fff; padding: 25px; border-radius: 10px; box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1); max-width: 420px; transform: scale(0.95); transition: transform 0.2s ease; width: 90%; }
        #custom-prompt-overlay.visible #custom-prompt-box { transform: scale(1); }
        #custom-prompt-message { margin-bottom: 15px; font-size: 1em; color: #495057; line-height: 1.6; white-space: normal; word-wrap: break-word; text-align: left; }
        #custom-prompt-input-container { margin-bottom: 20px; }
        #custom-prompt-input-container input[type="text"], #custom-prompt-input-container textarea {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            box-sizing: border-box;
            font-size: 0.95em;
            background: #ffffff;
            transition: all 0.2s ease;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }
        /* #custom-prompt-input-container input:focus, textarea:focus 样式已统一到全局聚焦效果系统中 */
        #custom-prompt-input-container textarea {
            resize: vertical;
            min-height: 80px;
            max-height: 200px;
            line-height: 1.5;
            overflow-y: auto;
        }
        #custom-prompt-buttons { display: flex; justify-content: flex-end; gap: 10px; }
        .custom-prompt-button { padding: 7px 16px; border: none; border-radius: 6px; cursor: pointer; font-size: 0.9em; font-weight: 500; transition: background-color 0.2s ease, box-shadow 0.2s ease; }
        #custom-prompt-ok { background-color: #007bff; color: white; } #custom-prompt-ok:hover { background-color: #0056b3; box-shadow: 0 2px 4px rgba(0, 123, 255, 0.2); }
        #custom-prompt-cancel { background-color: #6c757d; color: white; } #custom-prompt-cancel:hover { background-color: #5a6268; box-shadow: 0 2px 4px rgba(108, 117, 125, 0.2); }

        /* --- HTML Runner Modal --- */
        #html-runner-modal { display: none; position: fixed; inset: 0; background-color: rgba(0, 0, 0, 0.6); z-index: 1070; align-items: center; justify-content: center; opacity: 0; transition: opacity 0.2s ease; }
        #html-runner-modal.visible { display: flex; opacity: 1; }
        #html-runner-content { background-color: #fff; width: 90%; height: 85%; max-width: 1000px; border-radius: 8px; box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15); display: flex; flex-direction: column; overflow: hidden; transform: scale(0.95); transition: transform 0.2s ease; }
        #html-runner-modal.visible #html-runner-content { transform: scale(1); }
        #html-runner-header { padding: 10px 15px; background-color: #f1f3f5; border-bottom: 1px solid #dee2e6; display: flex; justify-content: space-between; align-items: center; flex-shrink: 0; }
        #html-runner-title { font-weight: 500; }
        #html-runner-close-button { background: none; border: none; font-size: 1.6em; color: #6c757d; cursor: pointer; line-height: 1; }
        #html-runner-iframe-container { flex-grow: 1; overflow: hidden; background-color: #fff; }
        #html-runner-iframe { width: 100%; height: 100%; border: none; display: block; }

        /* --- Code Viewer Modal (Always Dark) --- */
        #code-viewer-modal { display: none; position: fixed; inset: 0; background-color: rgba(0, 0, 0, 0.7); z-index: 1075; align-items: center; justify-content: center; opacity: 0; transition: opacity 0.2s ease; }
        #code-viewer-modal.visible { display: flex; opacity: 1; }
        #code-viewer-content { background-color: #252526; color: #e0e0e0; width: 90%; height: 85%; max-width: 800px; border-radius: 8px; box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3); display: flex; flex-direction: column; overflow: hidden; transform: scale(0.95); transition: transform 0.2s ease; }
        #code-viewer-modal.visible #code-viewer-content { transform: scale(1); }
        #code-viewer-header { display: flex; justify-content: space-between; align-items: center; background-color: rgba(255, 255, 255, 0.08); padding: 10px 15px; color: #ccc; border-bottom: 1px solid rgba(255, 255, 255, 0.18); flex-shrink: 0; }
        #code-viewer-title { font-weight: 500; color: #eee; }
        #code-viewer-header-buttons { display: flex; gap: 10px; align-items: center; }
        #code-viewer-copy-button, #code-viewer-download-button { background: none; border: none; color: #ccc; padding: 3px 6px; cursor: pointer; border-radius: 4px; transition: color 0.2s ease, background-color 0.2s ease; display: inline-flex; align-items: center; gap: 4px; font-size: 0.9em; }
        #code-viewer-copy-button:hover, #code-viewer-download-button:hover { color: #fff; background-color: rgba(255, 255, 255, 0.15); }
        #code-viewer-copy-button .fa-check { color: #4caf50; }
        #code-viewer-close-button { background: none; border: none; font-size: 1.6em; color: #aaa; cursor: pointer; line-height: 1; }
        #code-viewer-close-button:hover { color: #eee; }
        #code-viewer-code-container { flex-grow: 1; overflow: hidden; background-color: #1e1e1e; }
        #code-viewer-code-container pre { width: 100%; height: 100%; margin: 0; border-radius: 0; box-sizing: border-box; display: flex; flex-direction: column; border: none; background-color: #1e1e1e; color: #d4d4d4; box-shadow: none; }
        #code-viewer-code-container .code-scroll-wrapper { flex-grow: 1; overflow: auto; padding: 1em 1.2em; background-color: #1e1e1e; }
        #code-viewer-code-container pre code.hljs { display: block; background: none; color: inherit; line-height: 1.5; user-select: text; -webkit-user-select: text; padding: 0; min-width: 100%; box-sizing: border-box; width: max-content; }

        /* --- Toast Notifications (Light Theme Base) --- */
        #toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1100;
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 12px;
            width: max-content;
            max-width: 90%;
            pointer-events: none;
        }

        .toast-notification {
            background: var(--bg-surface);
            color: var(--text-primary);
            padding: 14px 18px;
            border-radius: 8px;
            font-size: 0.9em;
            font-weight: 400;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08);
            text-align: left;
            white-space: normal;
            word-wrap: break-word;
            pointer-events: auto;
            width: auto;
            max-width: 350px;
            border: 1px solid var(--border-secondary);
            position: relative;
            overflow: hidden;
        }

        .toast-notification.visible {
            opacity: 1;
            transform: translateX(0);
        }

        .toast-notification:hover {
            transform: translateY(-1px);
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12), 0 2px 4px rgba(0, 0, 0, 0.08);
        }

        /* 清新简约的Toast类型样式 */
        .toast-notification.success {
            background: #f0fdf4;
            color: #166534;
            border-color: #bbf7d0;
            box-shadow: 0 4px 12px rgba(34, 197, 94, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08);
        }

        .toast-notification.error {
            background: #fef2f2;
            color: #991b1b;
            border-color: #fecaca;
            box-shadow: 0 4px 12px rgba(239, 68, 68, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08);
        }

        .toast-notification.warning {
            background: #fffbeb;
            color: #92400e;
            border-color: #fed7aa;
            box-shadow: 0 4px 12px rgba(245, 158, 11, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08);
        }

        .toast-notification.info {
            background: #eff6ff;
            color: #1e40af;
            border-color: #bfdbfe;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08);
        }



        /* === 绘画界面全新设计 - 专业级布局 === */
        #image-gen-modal .modal-content {
            max-width: 680px;
            width: 92%;
            max-height: 92vh;
            overflow-y: auto;
            background: #ffffff;
            border-radius: 16px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
            border: 1px solid rgba(0, 0, 0, 0.08);
        }

        #image-gen-modal .modal-form-area {
            overflow-x: hidden;
            padding: 24px;
        }

        /* 现代化的选项容器 */
        #image-gen-options {
            display: flex;
            flex-direction: column;
            gap: 20px;
            margin-top: 20px;
        }

        /* 卡片式分组布局 */
        .image-gen-card {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 16px;
            transition: all 0.2s ease;
        }

        .image-gen-card:hover {
            border-color: #cbd5e1;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        }

        .image-gen-card-title {
            font-size: 0.85em;
            font-weight: 600;
            color: #475569;
            margin-bottom: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* 智能网格布局 */
        .image-gen-grid {
            display: grid;
            gap: 16px;
            align-items: end;
        }

        /* 基础参数：3列网格 */
        .image-gen-grid.basic-params {
            grid-template-columns: 1fr 1fr 1fr;
        }

        /* 尺寸参数：4列网格 */
        .image-gen-grid.size-params {
            grid-template-columns: 1fr 1fr 1fr 1fr;
        }

        /* 高级参数：2列网格 */
        .image-gen-grid.advanced-params {
            grid-template-columns: 1fr 1fr;
        }

        /* 切换开关：3列网格 */
        .image-gen-grid.toggle-params {
            grid-template-columns: repeat(3, 1fr);
        }
        /* Adjustments for specific controls */
        /* 图片生成模型容器样式 */
        .image-gen-model-container { 
            display: flex;
            align-items: center;
            gap: 8px;
            flex-wrap: wrap;
        }
        /* === 精致的表单组件设计 === */

        /* 统一的表单组样式 */
        .image-gen-form-group {
            display: flex;
            flex-direction: column;
            gap: 6px;
        }

        .image-gen-form-group label {
            font-size: 0.8em;
            font-weight: 500;
            color: #64748b;
            margin: 0;
            text-transform: uppercase;
            letter-spacing: 0.3px;
        }

        /* Referrer标签特殊处理 - 首字母大写 */
        .image-gen-form-group label[for="image-gen-referrer-input"] {
            text-transform: capitalize;
        }

        /* 现代化输入框样式 */
        .image-gen-input {
            padding: 10px 12px;
            border: 1.5px solid #e2e8f0;
            border-radius: 8px;
            font-size: 0.9em;
            background: #ffffff;
            transition: all 0.2s ease;
            height: 40px;
            box-sizing: border-box;
        }

        /* .image-gen-input:focus 样式已统一到全局聚焦效果系统中 */

        .image-gen-input:disabled {
            background: #f1f5f9;
            color: #94a3b8;
            cursor: not-allowed;
        }

        /* 数字输入框特殊样式 */
        .image-gen-input[type="number"] {
            text-align: center;
            font-weight: 500;
            width: 80px;
        }

        /* 种子值输入框特殊样式 */
        .image-gen-input[type="number"]#image-gen-seed-input {
            width: 140px; /* 加长以显示完整占位符 */
            text-align: left;
            padding-left: 12px;
        }

        /* 文本输入框 */
        .image-gen-input[type="text"] {
            width: 100%;
            max-width: 240px;
        }

        /* 自适应文本域样式 */
        .image-gen-textarea {
            padding: 12px;
            border: 1.5px solid #e2e8f0;
            border-radius: 8px;
            font-size: 0.9em;
            background: #ffffff;
            transition: all 0.2s ease;
            width: 100%;
            min-height: 80px;
            max-height: 200px;
            resize: vertical;
            font-family: inherit;
            line-height: 1.5;
        }

        /* .image-gen-textarea:focus 样式已统一到全局聚焦效果系统中 */

        .image-gen-textarea::placeholder {
            color: #9ca3af;
            font-style: italic;
        }

        /* 现代化选择器样式 */
        .image-gen-selector {
            position: relative;
            width: 100%;
            max-width: 160px;
        }

        .image-gen-selector .custom-selector-trigger {
            width: 100%;
            height: 40px;
            padding: 10px 12px;
            border: 1.5px solid #e2e8f0;
            border-radius: 8px;
            background: #ffffff;
            font-size: 0.9em;
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .image-gen-selector .custom-selector-trigger:hover {
            border-color: #cbd5e1;
        }

        .image-gen-selector .custom-selector-trigger:focus-visible {
            outline: none;
            border-color: var(--focus-border-color);
            box-shadow: 0 0 0 var(--focus-glow-radius) var(--focus-glow-color);
            transition: var(--focus-transition);
        }
        /* V48.1: Smaller random button */
        #image-gen-random-seed-button {
            padding: 6px 10px; font-size: 0.9em; height: 38px; flex-shrink: 0; line-height: 1; border: 1px solid #ced4da; background-color: #fff; color: #495057; border-radius: 6px; cursor: pointer; transition: background-color 0.2s;
        }
        #image-gen-random-seed-button:hover { background-color: #e9ecef; }
        /* === 现代化切换开关设计 === */
        .image-gen-toggle-group {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 10px 14px;
            background: #f8fafc;
            border: 1.5px solid #e2e8f0;
            border-radius: 8px;
            transition: all 0.2s ease;
            cursor: pointer;
            min-height: 40px;
            box-sizing: border-box;
        }

        .image-gen-toggle-group:hover {
            border-color: #cbd5e1;
            background: #f1f5f9;
        }

        .image-gen-toggle-group.checked {
            border-color: #3b82f6;
            background: #eff6ff;
        }

        .image-gen-toggle-group label {
            margin: 0;
            cursor: pointer;
            font-size: 0.85em;
            font-weight: 500;
            color: #475569;
            user-select: none;
        }

        .image-gen-toggle-group input[type="checkbox"] {
            width: 16px;
            height: 16px;
            cursor: pointer;
            accent-color: #3b82f6;
        }

        /* === 现代化图像生成按钮设计 === */
        .image-gen-action-button {
            padding: 8px 16px;
            border: 0.5px solid transparent;
            border-radius: 6px;
            font-size: 13px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            line-height: 20px;
        }

        .image-gen-action-button.primary {
            background: var(--color-primary);
            color: white;
            border-color: var(--color-primary);
        }

        .image-gen-action-button.primary:hover {
            background: var(--color-primary-hover);
            border-color: var(--color-primary-hover);
            transform: translateY(-0.5px);
        }

        .image-gen-action-button.secondary {
            background: var(--color-gray-100);
            color: var(--text-primary);
            border-color: var(--color-gray-300);
        }

        .image-gen-action-button.secondary:hover {
            background: var(--color-gray-200);
            border-color: var(--color-gray-400);
            transform: translateY(-0.5px);
        }

        /* 随机种子按钮特殊样式 */
        #image-gen-random-seed-button {
            width: 40px;
            height: 40px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 8px;
        }

        #image-gen-random-seed-button i {
            font-size: 0.9em;
        }

        /* 提示词操作按钮优化 - 小巧精致 */
        #image-gen-prompt-actions {
            display: flex;
            gap: 8px;
            margin-top: 8px;
            flex-wrap: wrap;
        }

        #image-gen-prompt-actions button {
            padding: 6px 12px;
            height: 32px;
            font-size: 0.8em;
            border-radius: 6px;
            border: 1px solid #e2e8f0;
            background: #f8fafc;
            color: #475569;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 4px;
            font-weight: 500;
        }

        #image-gen-prompt-actions button:hover:not(:disabled) {
            background: #f1f5f9;
            border-color: #cbd5e1;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        #image-gen-prompt-actions button:disabled {
            background: #f1f5f9;
            color: #94a3b8;
            cursor: not-allowed;
            opacity: 0.6;
        }

        #image-gen-prompt-actions button i {
            font-size: 0.85em;
        }

        /* 特定按钮颜色 */
        #enhance-prompt-button {
            background: #eff6ff;
            border-color: #bfdbfe;
            color: #1d4ed8;
        }

        #enhance-prompt-button:hover:not(:disabled) {
            background: #dbeafe;
            border-color: #93c5fd;
        }

        #clear-prompt-button {
            background: #fef2f2;
            border-color: #fecaca;
            color: #dc2626;
        }

        #clear-prompt-button:hover:not(:disabled) {
            background: #fee2e2;
            border-color: #fca5a5;
        }

        /* === 提示词优化底部栏样式 === */
        .prompt-optimization-bar {
            margin-top: 12px;
            padding: 12px;
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border: 1px solid #bae6fd;
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .prompt-optimization-bar:hover {
            background: linear-gradient(135deg, #e0f2fe 0%, #bae6fd 100%);
            border-color: #7dd3fc;
            box-shadow: 0 4px 12px rgba(14, 165, 233, 0.15);
        }

        .optimization-input-container {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .prompt-optimization-input {
            flex: 1;
            padding: 10px 16px;
            border: 1px solid #bae6fd;
            border-radius: 8px;
            font-size: 0.9em;
            background: #ffffff;
            transition: all 0.2s ease;
            color: #0f172a;
        }

        /* .prompt-optimization-input:focus 样式已统一到全局聚焦效果系统中 */

        .prompt-optimization-input::placeholder {
            color: #64748b;
            font-style: italic;
        }

        /* 通用占位符样式 - 确保所有输入框都使用斜体 */
        input::placeholder,
        textarea::placeholder {
            font-style: italic;
        }

        .prompt-optimization-send {
            padding: 10px 16px;
            background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 48px;
            font-weight: 500;
        }

        .prompt-optimization-send:hover:not(:disabled) {
            background: linear-gradient(135deg, #0284c7 0%, #0369a1 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(14, 165, 233, 0.3);
        }

        .prompt-optimization-send:disabled {
            background: #94a3b8;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .prompt-optimization-send i {
            font-size: 0.9em;
        }

        /* 黑夜模式适配 */
        body.dark-theme .prompt-optimization-bar {
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            border-color: #64b5f6;
        }

        body.dark-theme .prompt-optimization-bar:hover {
            background: linear-gradient(135deg, #334155 0%, #475569 100%);
            border-color: #93c5fd;
            box-shadow: 0 4px 12px rgba(100, 181, 246, 0.2);
        }

        body.dark-theme .prompt-optimization-input {
            background: #1a1a1a;
            border-color: #64b5f6;
            color: #e0e0e0;
        }

        /* body.dark-theme .prompt-optimization-input:focus 样式已统一到全局聚焦效果系统中 */

        body.dark-theme .prompt-optimization-input::placeholder {
            color: #94a3b8;
            font-style: italic;
        }

        body.dark-theme .prompt-optimization-send {
            background: linear-gradient(135deg, #1e40af 0%, #1d4ed8 100%);
        }

        body.dark-theme .prompt-optimization-send:hover:not(:disabled) {
            background: linear-gradient(135deg, #1d4ed8 0%, #2563eb 100%);
            box-shadow: 0 4px 12px rgba(100, 181, 246, 0.3);
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Media query for wider screens */
        @media (min-width: 700px) {
            .image-gen-options-row.row-1 { grid-template-columns: minmax(180px, 1.5fr) auto minmax(160px, 1fr); /* Model | Width/Height | Ratio */ }
            .image-gen-options-row.row-2 { grid-template-columns: auto 1fr; /* Seed+Random | Toggles */ }
        }
        /* Prompt Actions */
        #image-gen-prompt-actions { margin-top: 10px; display: flex; flex-wrap: wrap; gap: 8px; }
        #image-gen-prompt-actions button { padding: 5px 10px; font-size: 0.85em; background-color: #e9ecef; border: 1px solid #ced4da; border-radius: 4px; cursor: pointer; transition: background-color 0.2s; display: inline-flex; align-items: center; gap: 4px; }
        #image-gen-prompt-actions button:hover:not(:disabled) { background-color: #d1d5db; }
        #image-gen-prompt-actions button:disabled { opacity: 0.6; cursor: not-allowed; }
        #image-gen-prompt-actions button i { margin-right: 3px; }
        /* === 精致的单张生成区域设计 === */
        #image-gen-result-container {
            margin-top: 24px;
            padding: 20px;
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            text-align: center;
            /* 确保角标不被裁切 */
            overflow: visible;
        }

        #image-gen-result-container .result-header {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            margin-bottom: 16px;
            font-size: 0.9em;
            font-weight: 500;
            color: #475569;
        }

        #image-gen-result-container .result-header i {
            color: #10b981;
            font-size: 1.1em;
        }

        /* === 拟态风格智能装饰外框系统 === */
        #image-gen-result-container .image-display-frame {
            display: inline-block;
            border-radius: 16px;
            max-width: 100%;
            position: relative;
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);

            /* 拟态风格默认外框 */
            background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
            border: 1px solid rgba(226, 232, 240, 0.8);
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.06),
                0 2px 8px rgba(0, 0, 0, 0.04),
                inset 0 1px 0 rgba(255, 255, 255, 0.9),
                inset 0 -1px 0 rgba(0, 0, 0, 0.02);

            /* 确保角标不被裁切 */
            overflow: visible;
        }

        #image-gen-result-container .image-display-frame:hover {
            transform: translateY(-4px) scale(1.02);
            box-shadow:
                0 16px 48px rgba(0, 0, 0, 0.12),
                0 4px 16px rgba(0, 0, 0, 0.08),
                inset 0 1px 0 rgba(255, 255, 255, 1),
                inset 0 -1px 0 rgba(0, 0, 0, 0.03);
        }

        #image-gen-result-container img {
            max-width: 100%;
            max-height: 45vh;
            display: block;
            cursor: pointer;
            transition: transform 0.2s ease;
            /* 图片圆角裁切，与外框完全一致，避免角落间隙 */
            border-radius: 15px;
            overflow: hidden;
            /* 确保图片边缘与氛围光自然融合 */
            box-shadow:
                inset 0 0 0 1px rgba(255, 255, 255, 0.1),  /* 内部边框光效 */
                inset 0 0 20px rgba(0, 0, 0, 0.05);        /* 内部柔和阴影 */
        }

        #image-gen-result-container .image-display-frame:hover img {
            transform: scale(1.01);
        }

        /* === 个性化比例氛围光系统 === */

        /* 1:1 方形 - 头像氛围光 */
        #image-gen-result-container .image-display-frame.aspect-square {
            background: linear-gradient(145deg,
                #f0fdf4 0%,   /* 清新绿背景 */
                #dcfce7 25%,
                #ffffff 50%,
                #f9fafb 100%) !important;
            border: 2px solid rgba(16, 185, 129, 0.2) !important;
            border-radius: 20px !important;

            /* 头像场景的温暖氛围光 */
            box-shadow:
                0 0 30px rgba(16, 185, 129, 0.3),      /* 外围氛围光 */
                0 12px 40px rgba(16, 185, 129, 0.15),  /* 主阴影 */
                0 4px 16px rgba(16, 185, 129, 0.08),   /* 细节阴影 */
                inset 0 2px 0 rgba(255, 255, 255, 0.9),
                inset 0 -2px 0 rgba(16, 185, 129, 0.1) !important;
        }

        /* 16:9 宽屏 - 影院氛围光 */
        #image-gen-result-container .image-display-frame.aspect-landscape {
            background: linear-gradient(145deg,
                #eff6ff 0%,   /* 科技蓝背景 */
                #dbeafe 25%,
                #ffffff 50%,
                #f8fafc 100%) !important;
            border: 2px solid rgba(59, 130, 246, 0.2) !important;
            border-radius: 12px !important;

            /* 影院场景的科技氛围光 */
            box-shadow:
                0 0 35px rgba(59, 130, 246, 0.35),     /* 外围氛围光 */
                0 12px 40px rgba(59, 130, 246, 0.18),  /* 主阴影 */
                0 4px 16px rgba(59, 130, 246, 0.12),   /* 细节阴影 */
                inset 0 2px 0 rgba(255, 255, 255, 0.9),
                inset 0 -2px 0 rgba(59, 130, 246, 0.1) !important;
        }

        /* 9:16 竖屏 - 手机氛围光 */
        #image-gen-result-container .image-display-frame.aspect-portrait {
            background: linear-gradient(145deg,
                #faf5ff 0%,   /* 优雅紫背景 */
                #f3e8ff 25%,
                #ffffff 50%,
                #f9fafb 100%) !important;
            border: 2px solid rgba(139, 92, 246, 0.2) !important;
            border-radius: 24px !important;

            /* 手机场景的现代氛围光 */
            box-shadow:
                0 0 32px rgba(139, 92, 246, 0.32),     /* 外围氛围光 */
                0 12px 40px rgba(139, 92, 246, 0.16),  /* 主阴影 */
                0 4px 16px rgba(139, 92, 246, 0.10),   /* 细节阴影 */
                inset 0 2px 0 rgba(255, 255, 255, 0.9),
                inset 0 -2px 0 rgba(139, 92, 246, 0.08) !important;
        }



        /* === 个性化比例标签系统 === */

        /* 基础标签样式 */
        #image-gen-result-container .image-display-frame::after {
            position: absolute;
            top: var(--spacing-sm);
            right: var(--spacing-sm);

            /* 使用设计系统的徽章样式 */
            display: inline-flex;
            align-items: center;
            gap: var(--spacing-xs);
            padding: var(--spacing-xs) var(--spacing-sm);
            font-size: var(--font-size-xs);
            font-weight: var(--font-weight-semibold);
            line-height: 1;
            white-space: nowrap;
            border-radius: var(--radius-full);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);

            /* 设计系统的阴影 */
            box-shadow: var(--shadow-md);
            backdrop-filter: blur(8px);

            /* 优雅的动画 */
            opacity: 0;
            transform: translateX(var(--spacing-sm)) scale(0.95);
            transition: all var(--duration-medium) var(--ease-smooth);
            z-index: var(--z-tooltip);
        }

        #image-gen-result-container .image-display-frame:hover::after {
            opacity: 1;
            transform: translateX(0) scale(1);
            box-shadow: var(--shadow-lg);
        }

        /* 1:1 方形 - 头像场景 */
        #image-gen-result-container .image-display-frame.aspect-square::after {
            content: '👤 头像';
            background-color: #10b981; /* 翠绿色 */
        }

        /* 16:9 宽屏 - 影院场景 */
        #image-gen-result-container .image-display-frame.aspect-landscape::after {
            content: '🎬 影院';
            background-color: #3b82f6; /* 蓝色 */
        }

        /* 9:16 竖屏 - 手机场景 */
        #image-gen-result-container .image-display-frame.aspect-portrait::after {
            content: '📱 手机';
            background-color: #8b5cf6; /* 紫色 */
        }

        /* 4:3 标准 - 相机氛围光 */
        #image-gen-result-container .image-display-frame.aspect-standard {
            background: linear-gradient(145deg,
                #fffbeb 0%,   /* 温暖琥珀背景 */
                #fef3c7 25%,
                #ffffff 50%,
                #f9fafb 100%) !important;
            border: 2px solid rgba(245, 158, 11, 0.2) !important;
            border-radius: 16px !important;

            /* 相机场景的温暖氛围光 */
            box-shadow:
                0 0 28px rgba(245, 158, 11, 0.28),     /* 外围氛围光 */
                0 12px 40px rgba(245, 158, 11, 0.15),  /* 主阴影 */
                0 4px 16px rgba(245, 158, 11, 0.08),   /* 细节阴影 */
                inset 0 2px 0 rgba(255, 255, 255, 0.9),
                inset 0 -2px 0 rgba(245, 158, 11, 0.1) !important;
        }

        /* 4:3 标准 - 相机场景标签 */
        #image-gen-result-container .image-display-frame.aspect-standard::after {
            content: '📷 相机';
            background-color: #f59e0b; /* 琥珀色 */
        }

        /* 3:4 竖版 - 海报氛围光 */
        #image-gen-result-container .image-display-frame.aspect-poster {
            background: linear-gradient(145deg,
                #fef2f2 0%,   /* 醒目红色背景 */
                #fecaca 25%,
                #ffffff 50%,
                #f9fafb 100%) !important;
            border: 2px solid rgba(239, 68, 68, 0.2) !important;
            border-radius: 18px !important;

            /* 海报场景的醒目氛围光 */
            box-shadow:
                0 0 30px rgba(239, 68, 68, 0.30),      /* 外围氛围光 */
                0 12px 40px rgba(239, 68, 68, 0.16),   /* 主阴影 */
                0 4px 16px rgba(239, 68, 68, 0.10),    /* 细节阴影 */
                inset 0 2px 0 rgba(255, 255, 255, 0.9),
                inset 0 -2px 0 rgba(239, 68, 68, 0.08) !important;
        }

        /* 3:4 竖版 - 海报场景标签 */
        #image-gen-result-container .image-display-frame.aspect-poster::after {
            content: '📄 海报';
            background-color: #ef4444; /* 红色 */
        }

        /* 21:9 电影 - 大片氛围光 */
        #image-gen-result-container .image-display-frame.aspect-cinema {
            background: linear-gradient(145deg,
                #eef2ff 0%,   /* 高端靛蓝背景 */
                #e0e7ff 25%,
                #ffffff 50%,
                #f8fafc 100%) !important;
            border: 2px solid rgba(99, 102, 241, 0.2) !important;
            border-radius: 10px !important;

            /* 大片场景的高端氛围光 */
            box-shadow:
                0 0 40px rgba(99, 102, 241, 0.40),     /* 外围氛围光 */
                0 15px 50px rgba(99, 102, 241, 0.20),  /* 主阴影 */
                0 5px 20px rgba(99, 102, 241, 0.12),   /* 细节阴影 */
                inset 0 2px 0 rgba(255, 255, 255, 0.9),
                inset 0 -2px 0 rgba(99, 102, 241, 0.1) !important;
        }

        /* 21:9 电影 - 大片场景标签 */
        #image-gen-result-container .image-display-frame.aspect-cinema::after {
            content: '🎭 大片';
            background-color: #6366f1; /* 靛蓝色 */
        }

        /* 2:3 书籍 - 封面氛围光 */
        #image-gen-result-container .image-display-frame.aspect-book {
            background: linear-gradient(145deg,
                #f5f3ff 0%,   /* 文化深紫背景 */
                #ede9fe 25%,
                #ffffff 50%,
                #f9fafb 100%) !important;
            border: 2px solid rgba(124, 58, 237, 0.2) !important;
            border-radius: 22px !important;

            /* 封面场景的文化氛围光 */
            box-shadow:
                0 0 33px rgba(124, 58, 237, 0.33),     /* 外围氛围光 */
                0 12px 40px rgba(124, 58, 237, 0.17),  /* 主阴影 */
                0 4px 16px rgba(124, 58, 237, 0.09),   /* 细节阴影 */
                inset 0 2px 0 rgba(255, 255, 255, 0.9),
                inset 0 -2px 0 rgba(124, 58, 237, 0.08) !important;
        }

        /* 2:3 书籍 - 封面场景标签 */
        #image-gen-result-container .image-display-frame.aspect-book::after {
            content: '📚 封面';
            background-color: #7c3aed; /* 深紫色 */
        }

        /* 3:2 摄影 - 风景氛围光 */
        #image-gen-result-container .image-display-frame.aspect-photo {
            background: linear-gradient(145deg,
                #ecfeff 0%,   /* 自然青色背景 */
                #cffafe 25%,
                #ffffff 50%,
                #f8fafc 100%) !important;
            border: 2px solid rgba(6, 182, 212, 0.2) !important;
            border-radius: 14px !important;

            /* 风景场景的自然氛围光 */
            box-shadow:
                0 0 35px rgba(6, 182, 212, 0.35),      /* 外围氛围光 */
                0 12px 40px rgba(6, 182, 212, 0.18),   /* 主阴影 */
                0 4px 16px rgba(6, 182, 212, 0.10),    /* 细节阴影 */
                inset 0 2px 0 rgba(255, 255, 255, 0.9),
                inset 0 -2px 0 rgba(6, 182, 212, 0.08) !important;
        }

        /* 3:2 摄影 - 风景场景标签 */
        #image-gen-result-container .image-display-frame.aspect-photo::after {
            content: '🌄 风景';
            background-color: #06b6d4; /* 青色 */
        }

        /* 5:4 画框 - 艺术氛围光 */
        #image-gen-result-container .image-display-frame.aspect-art {
            background: linear-gradient(145deg,
                #ecfdf5 0%,   /* 艺术深绿背景 */
                #d1fae5 25%,
                #ffffff 50%,
                #f9fafb 100%) !important;
            border: 2px solid rgba(5, 150, 105, 0.2) !important;
            border-radius: 25px !important;

            /* 艺术场景的典雅氛围光 */
            box-shadow:
                0 0 38px rgba(5, 150, 105, 0.38),      /* 外围氛围光 */
                0 15px 45px rgba(5, 150, 105, 0.19),   /* 主阴影 */
                0 5px 18px rgba(5, 150, 105, 0.11),    /* 细节阴影 */
                inset 0 2px 0 rgba(255, 255, 255, 0.9),
                inset 0 -2px 0 rgba(5, 150, 105, 0.1) !important;
        }

        /* 5:4 画框 - 艺术场景标签 */
        #image-gen-result-container .image-display-frame.aspect-art::after {
            content: '🖼️ 画框';
            background-color: #059669; /* 深绿色 */
        }

        /* 自定义比例 - 创意氛围光 */
        #image-gen-result-container .image-display-frame.aspect-custom {
            background: linear-gradient(145deg,
                #fdf2f8 0%,   /* 创意粉色背景 */
                #fce7f3 25%,
                #ffffff 50%,
                #f9fafb 100%) !important;
            border: 2px solid rgba(236, 72, 153, 0.2) !important;
            border-radius: 28px !important;

            /* 创意场景的梦幻氛围光 */
            box-shadow:
                0 0 42px rgba(236, 72, 153, 0.42),     /* 外围氛围光 */
                0 18px 50px rgba(236, 72, 153, 0.21),  /* 主阴影 */
                0 6px 20px rgba(236, 72, 153, 0.13),   /* 细节阴影 */
                inset 0 2px 0 rgba(255, 255, 255, 0.9),
                inset 0 -2px 0 rgba(236, 72, 153, 0.1) !important;
        }

        /* 自定义比例 - 创意场景标签 */
        #image-gen-result-container .image-display-frame.aspect-custom::after {
            content: '✨ 创意';
            background-color: #ec4899; /* 粉色 */
        }

        /* === 悬停时氛围光增强效果 === */

        /* 头像悬停增强 */
        #image-gen-result-container .image-display-frame.aspect-square:hover {
            box-shadow:
                0 0 50px rgba(16, 185, 129, 0.5),      /* 增强氛围光 */
                0 20px 60px rgba(16, 185, 129, 0.25),  /* 增强主阴影 */
                0 8px 25px rgba(16, 185, 129, 0.15),   /* 增强细节阴影 */
                inset 0 2px 0 rgba(255, 255, 255, 1),
                inset 0 -2px 0 rgba(16, 185, 129, 0.15) !important;
        }

        /* 影院悬停增强 */
        #image-gen-result-container .image-display-frame.aspect-landscape:hover {
            box-shadow:
                0 0 55px rgba(59, 130, 246, 0.55),     /* 增强氛围光 */
                0 20px 60px rgba(59, 130, 246, 0.28),  /* 增强主阴影 */
                0 8px 25px rgba(59, 130, 246, 0.18),   /* 增强细节阴影 */
                inset 0 2px 0 rgba(255, 255, 255, 1),
                inset 0 -2px 0 rgba(59, 130, 246, 0.15) !important;
        }

        /* 手机悬停增强 */
        #image-gen-result-container .image-display-frame.aspect-portrait:hover {
            box-shadow:
                0 0 52px rgba(139, 92, 246, 0.52),     /* 增强氛围光 */
                0 20px 60px rgba(139, 92, 246, 0.26),  /* 增强主阴影 */
                0 8px 25px rgba(139, 92, 246, 0.16),   /* 增强细节阴影 */
                inset 0 2px 0 rgba(255, 255, 255, 1),
                inset 0 -2px 0 rgba(139, 92, 246, 0.12) !important;
        }

        /* 相机悬停增强 */
        #image-gen-result-container .image-display-frame.aspect-standard:hover {
            box-shadow:
                0 0 48px rgba(245, 158, 11, 0.48),     /* 增强氛围光 */
                0 20px 60px rgba(245, 158, 11, 0.24),  /* 增强主阴影 */
                0 8px 25px rgba(245, 158, 11, 0.14),   /* 增强细节阴影 */
                inset 0 2px 0 rgba(255, 255, 255, 1),
                inset 0 -2px 0 rgba(245, 158, 11, 0.15) !important;
        }

        /* 海报悬停增强 */
        #image-gen-result-container .image-display-frame.aspect-poster:hover {
            box-shadow:
                0 0 50px rgba(239, 68, 68, 0.50),      /* 增强氛围光 */
                0 20px 60px rgba(239, 68, 68, 0.25),   /* 增强主阴影 */
                0 8px 25px rgba(239, 68, 68, 0.15),    /* 增强细节阴影 */
                inset 0 2px 0 rgba(255, 255, 255, 1),
                inset 0 -2px 0 rgba(239, 68, 68, 0.12) !important;
        }

        /* 大片悬停增强 */
        #image-gen-result-container .image-display-frame.aspect-cinema:hover {
            box-shadow:
                0 0 60px rgba(99, 102, 241, 0.60),     /* 增强氛围光 */
                0 25px 70px rgba(99, 102, 241, 0.30),  /* 增强主阴影 */
                0 10px 30px rgba(99, 102, 241, 0.18),  /* 增强细节阴影 */
                inset 0 2px 0 rgba(255, 255, 255, 1),
                inset 0 -2px 0 rgba(99, 102, 241, 0.15) !important;
        }

        /* 封面悬停增强 */
        #image-gen-result-container .image-display-frame.aspect-book:hover {
            box-shadow:
                0 0 53px rgba(124, 58, 237, 0.53),     /* 增强氛围光 */
                0 20px 60px rgba(124, 58, 237, 0.27),  /* 增强主阴影 */
                0 8px 25px rgba(124, 58, 237, 0.15),   /* 增强细节阴影 */
                inset 0 2px 0 rgba(255, 255, 255, 1),
                inset 0 -2px 0 rgba(124, 58, 237, 0.12) !important;
        }

        /* 风景悬停增强 */
        #image-gen-result-container .image-display-frame.aspect-photo:hover {
            box-shadow:
                0 0 55px rgba(6, 182, 212, 0.55),      /* 增强氛围光 */
                0 20px 60px rgba(6, 182, 212, 0.28),   /* 增强主阴影 */
                0 8px 25px rgba(6, 182, 212, 0.16),    /* 增强细节阴影 */
                inset 0 2px 0 rgba(255, 255, 255, 1),
                inset 0 -2px 0 rgba(6, 182, 212, 0.12) !important;
        }

        /* 艺术悬停增强 */
        #image-gen-result-container .image-display-frame.aspect-art:hover {
            box-shadow:
                0 0 58px rgba(5, 150, 105, 0.58),      /* 增强氛围光 */
                0 25px 65px rgba(5, 150, 105, 0.29),   /* 增强主阴影 */
                0 10px 28px rgba(5, 150, 105, 0.17),   /* 增强细节阴影 */
                inset 0 2px 0 rgba(255, 255, 255, 1),
                inset 0 -2px 0 rgba(5, 150, 105, 0.15) !important;
        }

        /* 创意悬停增强 */
        #image-gen-result-container .image-display-frame.aspect-custom:hover {
            box-shadow:
                0 0 62px rgba(236, 72, 153, 0.62),     /* 增强氛围光 */
                0 28px 70px rgba(236, 72, 153, 0.31),  /* 增强主阴影 */
                0 12px 30px rgba(236, 72, 153, 0.19),  /* 增强细节阴影 */
                inset 0 2px 0 rgba(255, 255, 255, 1),
                inset 0 -2px 0 rgba(236, 72, 153, 0.15) !important;
        }

        #image-gen-result-container .image-display-frame.aspect-square:hover {
            box-shadow:
                0 20px 60px rgba(16, 185, 129, 0.25),
                0 8px 24px rgba(16, 185, 129, 0.15),
                inset 0 2px 0 rgba(255, 255, 255, 1),
                inset 0 -2px 0 rgba(16, 185, 129, 0.15),
                inset 2px 0 0 rgba(16, 185, 129, 0.08),
                inset -2px 0 0 rgba(16, 185, 129, 0.08) !important;
        }

        /* 横向图片装饰框 (16:9, 4:3等) - 电影宽屏风格 */
        #image-gen-result-container .image-display-frame.aspect-landscape {
            /* 电影宽屏的拟态设计 */
            background: linear-gradient(145deg,
                #eff6ff 0%,
                #dbeafe 25%,
                #ffffff 50%,
                #f8fafc 100%) !important;
            border: 2px solid transparent !important;
            border-radius: 12px !important;
            position: relative;

            /* 电影感的阴影效果 */
            box-shadow:
                0 8px 32px rgba(59, 130, 246, 0.18),
                0 3px 12px rgba(59, 130, 246, 0.12),
                inset 0 1px 0 rgba(255, 255, 255, 0.9),
                inset 0 -1px 0 rgba(59, 130, 246, 0.08),
                inset 1px 0 0 rgba(59, 130, 246, 0.04),
                inset -1px 0 0 rgba(59, 130, 246, 0.04) !important;
        }





        #image-gen-result-container .image-display-frame.aspect-landscape:hover {
            box-shadow:
                0 16px 48px rgba(59, 130, 246, 0.25),
                0 6px 20px rgba(59, 130, 246, 0.18),
                inset 0 1px 0 rgba(255, 255, 255, 1),
                inset 0 -1px 0 rgba(59, 130, 246, 0.12),
                inset 1px 0 0 rgba(59, 130, 246, 0.06),
                inset -1px 0 0 rgba(59, 130, 246, 0.06) !important;
        }

        /* 纵向图片装饰框 (9:16, 3:4等) - 现代移动端风格 */
        #image-gen-result-container .image-display-frame.aspect-portrait {
            /* 现代移动设备的拟态设计 */
            background: linear-gradient(145deg,
                #faf5ff 0%,
                #f3e8ff 25%,
                #ffffff 50%,
                #f9fafb 100%) !important;
            border: 2px solid transparent !important;
            border-radius: 24px !important;
            position: relative;

            /* 科技感的阴影效果 */
            box-shadow:
                0 12px 40px rgba(139, 92, 246, 0.16),
                0 4px 16px rgba(139, 92, 246, 0.10),
                inset 0 2px 0 rgba(255, 255, 255, 0.9),
                inset 0 -2px 0 rgba(139, 92, 246, 0.08),
                inset 2px 0 0 rgba(139, 92, 246, 0.04),
                inset -2px 0 0 rgba(139, 92, 246, 0.04) !important;
        }





        #image-gen-result-container .image-display-frame.aspect-portrait:hover {
            box-shadow:
                0 20px 60px rgba(139, 92, 246, 0.22),
                0 8px 24px rgba(139, 92, 246, 0.16),
                inset 0 2px 0 rgba(255, 255, 255, 1),
                inset 0 -2px 0 rgba(139, 92, 246, 0.12),
                inset 2px 0 0 rgba(139, 92, 246, 0.06),
                inset -2px 0 0 rgba(139, 92, 246, 0.06) !important;
        }

        /* === 精致的单张预览区域设计 === */
        #image-gen-preview-container {
            margin-top: 24px;
            padding: 20px;
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            text-align: center;
        }

        #image-gen-preview-container .preview-header {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            margin-bottom: 16px;
            font-size: 0.9em;
            font-weight: 500;
            color: #475569;
        }

        #image-gen-preview-container .preview-header i {
            color: #3b82f6;
            font-size: 1.1em;
            animation: pulse 2s infinite;
        }

        .image-preview-frame {
            display: inline-block;
            border: 2px dashed #3b82f6;
            border-radius: 16px;
            background: #ffffff;
            position: relative;
            min-width: 200px;
            min-height: 150px;
            max-width: 100%;
            max-height: 70vh;
            overflow: hidden;
            box-shadow:
                0 4px 16px rgba(59, 130, 246, 0.1),
                0 2px 8px rgba(59, 130, 246, 0.05),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            animation: dashPulse 3s ease-in-out infinite;
            /* 确保预览框能够适应动态尺寸 */
            flex-shrink: 0;
        }

        @keyframes dashPulse {
            0%, 100% {
                border-color: #3b82f6;
                box-shadow:
                    0 4px 16px rgba(59, 130, 246, 0.1),
                    0 2px 8px rgba(59, 130, 246, 0.05),
                    inset 0 1px 0 rgba(255, 255, 255, 0.8);
            }
            50% {
                border-color: #60a5fa;
                box-shadow:
                    0 6px 20px rgba(59, 130, 246, 0.15),
                    0 3px 12px rgba(59, 130, 246, 0.08),
                    inset 0 1px 0 rgba(255, 255, 255, 0.9);
            }
        }

        .image-preview-frame.loading {
            background:
                radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
                linear-gradient(45deg, rgba(59, 130, 246, 0.05) 25%, transparent 25%),
                linear-gradient(-45deg, rgba(59, 130, 246, 0.05) 25%, transparent 25%),
                linear-gradient(45deg, transparent 75%, rgba(59, 130, 246, 0.05) 75%),
                linear-gradient(-45deg, transparent 75%, rgba(59, 130, 246, 0.05) 75%),
                #ffffff;
            background-size: 32px 32px, 32px 32px, 16px 16px, 16px 16px, 16px 16px, 16px 16px;
            animation:
                dashPulse 3s ease-in-out infinite,
                previewLoading 2s linear infinite;
        }

        @keyframes previewLoading {
            0% {
                background-position: 0 0, 32px 32px, 0 0, 0 8px, 8px -8px, -8px 0px;
            }
            100% {
                background-position: 32px 32px, 64px 64px, 16px 16px, 16px 24px, 24px 8px, 8px 16px;
            }
        }

        .preview-loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 12px;
            color: #3b82f6;
            font-size: 0.9em;
            font-weight: 500;
        }

        .preview-loading i {
            font-size: 1.8em;
            opacity: 0.8;
        }

        /* === 精致的批量生成区域设计 === */
        #image-gen-batch-preview-container {
            margin-top: 24px;
            padding: 20px;
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
        }

        .batch-progress-header {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            margin-bottom: 20px;
        }

        .batch-progress-text {
            font-size: 0.9em;
            font-weight: 500;
            color: #475569;
        }

        .batch-progress-bar {
            width: 120px;
            height: 6px;
            background: #e2e8f0;
            border-radius: 3px;
            overflow: hidden;
        }

        .batch-progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3b82f6, #1d4ed8);
            border-radius: 3px;
            transition: width 0.3s ease;
            width: 0%;
        }

        /* 智能网格布局 - 根据数量自动调整 */
        .image-batch-grid {
            display: grid;
            gap: 16px;
            justify-content: center;
            max-width: 100%;
        }

        /* 1-2张：1列 */
        .image-batch-grid[data-count="1"],
        .image-batch-grid[data-count="2"] {
            grid-template-columns: repeat(auto-fit, minmax(200px, 200px));
        }

        /* 3-4张：2列 */
        .image-batch-grid[data-count="3"],
        .image-batch-grid[data-count="4"] {
            grid-template-columns: repeat(2, minmax(180px, 180px));
        }

        /* 5-6张：3列 */
        .image-batch-grid[data-count="5"],
        .image-batch-grid[data-count="6"] {
            grid-template-columns: repeat(3, minmax(160px, 160px));
        }

        /* 7-9张：3列 */
        .image-batch-grid[data-count="7"],
        .image-batch-grid[data-count="8"],
        .image-batch-grid[data-count="9"] {
            grid-template-columns: repeat(3, minmax(150px, 150px));
        }

        /* 10+张：4列，调整最大宽度避免超出 */
        .image-batch-grid[data-count="10"],
        .image-batch-grid[data-count="11"],
        .image-batch-grid[data-count="12"],
        .image-batch-grid[data-count="13"],
        .image-batch-grid[data-count="14"],
        .image-batch-grid[data-count="15"],
        .image-batch-grid[data-count="16"] {
            grid-template-columns: repeat(4, minmax(120px, 1fr));
            max-width: 100%;
        }

        .batch-image-item {
            position: relative;
            border: 1px solid #e2e8f0;
            border-radius: 6px; /* 进一步减少圆角 */
            overflow: hidden;
            background: #ffffff;
            width: 100%;
            height: 0;
            padding-bottom: 100%; /* 默认1:1比例，将动态调整 */
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        }

        /* 确保内容完全在容器内 */
        .batch-image-item * {
            border-radius: inherit;
        }

        .batch-image-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
        }

        .batch-image-item.loading {
            border-color: #3b82f6;
            background: linear-gradient(45deg, #f8fafc 25%, transparent 25%),
                        linear-gradient(-45deg, #f8fafc 25%, transparent 25%),
                        linear-gradient(45deg, transparent 75%, #f8fafc 75%),
                        linear-gradient(-45deg, transparent 75%, #f8fafc 75%);
            background-size: 16px 16px;
            animation: batchLoading 1.5s linear infinite;
        }

        .batch-image-item.completed {
            border-color: #10b981;
        }

        .batch-image-item img {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        .batch-image-item:hover img {
            transform: scale(1.02);
        }

        .batch-loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(4px);
            color: #3b82f6;
            font-size: 0.8em;
            font-weight: 500;
            gap: 8px;
        }

        .batch-loading-overlay i {
            font-size: 1.4em;
            opacity: 0.8;
        }

        @keyframes batchLoading {
            0% { background-position: 0 0, 0 8px, 8px -8px, -8px 0px; }
            100% { background-position: 16px 16px, 16px 24px, 24px 8px, 8px 16px; }
        }

        /* === 黑夜模式适配 === */
        body.dark-theme #image-gen-result-container,
        body.dark-theme #image-gen-preview-container,
        body.dark-theme #image-gen-batch-preview-container {
            background: #1a1a1a;
            border-color: #64b5f6;
        }

        body.dark-theme #image-gen-result-container .result-header,
        body.dark-theme #image-gen-preview-container .preview-header,
        body.dark-theme .batch-progress-text {
            color: #e0e0e0;
        }

        /* === 黑夜模式拟态风格基础外框 - 优化氛围光过渡 === */
        body.dark-theme #image-gen-result-container .image-display-frame {
            background: linear-gradient(145deg,
                rgba(42, 42, 42, 0.95) 0%,
                rgba(31, 31, 31, 0.98) 100%);
            border: 1px solid rgba(100, 181, 246, 0.25);
            /* 优化未选中状态的柔和氛围光 */
            box-shadow:
                0 0 20px rgba(100, 181, 246, 0.06),      /* 外围柔和光晕 */
                0 4px 16px rgba(100, 181, 246, 0.04),    /* 主要阴影 */
                0 1px 4px rgba(100, 181, 246, 0.02),     /* 细节阴影 */
                inset 0 1px 0 rgba(255, 255, 255, 0.03), /* 内部高光 */
                inset 0 -1px 0 rgba(0, 0, 0, 0.15);      /* 内部阴影 */
            /* 平滑过渡效果 */
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            /* 确保内容圆角一致性 */
            overflow: hidden;
        }

        /* 暗黑主题下图片的圆角和边缘优化 */
        body.dark-theme #image-gen-result-container img {
            border-radius: 15px;
            /* 暗黑主题下的图片边缘优化 */
            box-shadow:
                inset 0 0 0 1px rgba(100, 181, 246, 0.08),  /* 内部蓝色边框光效 */
                inset 0 0 30px rgba(0, 0, 0, 0.3),          /* 内部柔和阴影，增强深度 */
                inset 0 0 60px rgba(0, 0, 0, 0.1);          /* 更大范围的内部阴影 */
        }

        body.dark-theme #image-gen-result-container .image-display-frame:hover {
            border-color: rgba(100, 181, 246, 0.4);
            /* 悬停时增强但保持自然的氛围光 */
            box-shadow:
                0 0 40px rgba(100, 181, 246, 0.12),      /* 增强外围光晕 */
                0 8px 32px rgba(100, 181, 246, 0.08),    /* 增强主要阴影 */
                0 2px 8px rgba(100, 181, 246, 0.04),     /* 增强细节阴影 */
                inset 0 1px 0 rgba(255, 255, 255, 0.06), /* 增强内部高光 */
                inset 0 -1px 0 rgba(0, 0, 0, 0.2);       /* 增强内部阴影 */
        }

        /* 批量生图项目 - 优化氛围光 */
        body.dark-theme .batch-image-item {
            background: rgba(42, 42, 42, 0.95);
            border-color: rgba(100, 181, 246, 0.25);
            /* 未选中状态的柔和氛围光 */
            box-shadow:
                0 0 16px rgba(100, 181, 246, 0.06),      /* 外围柔和光晕 */
                0 2px 8px rgba(100, 181, 246, 0.04),     /* 主要阴影 */
                0 1px 3px rgba(100, 181, 246, 0.02);     /* 细节阴影 */
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        body.dark-theme .batch-image-item:hover {
            border-color: rgba(100, 181, 246, 0.4);
            /* 悬停时增强但保持自然的氛围光 */
            box-shadow:
                0 0 24px rgba(100, 181, 246, 0.12),      /* 增强外围光晕 */
                0 6px 20px rgba(100, 181, 246, 0.08),    /* 增强主要阴影 */
                0 2px 8px rgba(100, 181, 246, 0.04);     /* 增强细节阴影 */
        }

        /* 图片预览框 - 优化氛围光和动画 */
        body.dark-theme .image-preview-frame {
            background: rgba(42, 42, 42, 0.95);
            border: 2px dashed rgba(100, 181, 246, 0.4);
            /* 柔和的预览框氛围光 */
            box-shadow:
                0 0 20px rgba(100, 181, 246, 0.08),      /* 外围柔和光晕 */
                0 4px 16px rgba(100, 181, 246, 0.06),    /* 主要阴影 */
                0 2px 8px rgba(100, 181, 246, 0.04),     /* 细节阴影 */
                inset 0 1px 0 rgba(255, 255, 255, 0.05); /* 内部高光 */
            animation: dashPulseDark 3s ease-in-out infinite;
        }

        @keyframes dashPulseDark {
            0%, 100% {
                border-color: rgba(100, 181, 246, 0.4);
                box-shadow:
                    0 0 20px rgba(100, 181, 246, 0.08),
                    0 4px 16px rgba(100, 181, 246, 0.06),
                    0 2px 8px rgba(100, 181, 246, 0.04),
                    inset 0 1px 0 rgba(255, 255, 255, 0.05);
            }
            50% {
                border-color: rgba(144, 202, 249, 0.6);
                box-shadow:
                    0 0 30px rgba(100, 181, 246, 0.12),
                    0 6px 20px rgba(100, 181, 246, 0.1),
                    0 3px 12px rgba(100, 181, 246, 0.06),
                    inset 0 1px 0 rgba(255, 255, 255, 0.08);
            }
        }

        /* === 黑夜模式拟态装饰外框 === */

        /* 黑夜模式 - 艺术画框 (1:1方形) - 优化氛围光 */
        body.dark-theme #image-gen-result-container .image-display-frame.aspect-square {
            background: linear-gradient(145deg,
                rgba(6, 78, 59, 0.3) 0%,
                rgba(6, 95, 70, 0.2) 25%,
                rgba(42, 42, 42, 0.95) 50%,
                rgba(31, 31, 31, 0.98) 100%) !important;
            border-color: rgba(16, 185, 129, 0.3) !important;
            border-radius: 20px !important; /* 与白天主题保持一致 */
            /* 未选中状态的柔和绿色氛围光 */
            box-shadow:
                0 0 30px rgba(16, 185, 129, 0.08),       /* 外围柔和光晕 */
                0 6px 24px rgba(16, 185, 129, 0.06),     /* 主要阴影 */
                0 2px 8px rgba(16, 185, 129, 0.04),      /* 细节阴影 */
                inset 0 1px 0 rgba(255, 255, 255, 0.03), /* 内部高光 */
                inset 0 -1px 0 rgba(16, 185, 129, 0.08), /* 内部绿色阴影 */
                inset 1px 0 0 rgba(16, 185, 129, 0.04),  /* 左侧内光 */
                inset -1px 0 0 rgba(16, 185, 129, 0.04) !important; /* 右侧内光 */
        }

        /* 方形比例下的图片圆角优化 */
        body.dark-theme #image-gen-result-container .image-display-frame.aspect-square img {
            border-radius: 18px; /* 比外框小2px，确保完美贴合 */
            box-shadow:
                inset 0 0 0 1px rgba(16, 185, 129, 0.12),  /* 绿色内边框 */
                inset 0 0 25px rgba(0, 0, 0, 0.25),        /* 内部阴影 */
                inset 0 0 50px rgba(6, 78, 59, 0.1);       /* 绿色内部光晕 */
        }

        body.dark-theme #image-gen-result-container .image-display-frame.aspect-square:hover {
            border-color: rgba(16, 185, 129, 0.5) !important;
            /* 悬停时增强但保持自然的绿色氛围光 */
            box-shadow:
                0 0 50px rgba(16, 185, 129, 0.15),       /* 增强外围光晕 */
                0 12px 40px rgba(16, 185, 129, 0.12),    /* 增强主要阴影 */
                0 4px 16px rgba(16, 185, 129, 0.08),     /* 增强细节阴影 */
                inset 0 1px 0 rgba(255, 255, 255, 0.05), /* 增强内部高光 */
                inset 0 -1px 0 rgba(16, 185, 129, 0.12), /* 增强内部绿色阴影 */
                inset 1px 0 0 rgba(16, 185, 129, 0.06),  /* 增强左侧内光 */
                inset -1px 0 0 rgba(16, 185, 129, 0.06) !important; /* 增强右侧内光 */
        }

        /* 黑夜模式 - 电影宽屏 (16:9横屏) - 优化氛围光 */
        body.dark-theme #image-gen-result-container .image-display-frame.aspect-landscape {
            background: linear-gradient(145deg,
                rgba(30, 58, 138, 0.25) 0%,
                rgba(30, 64, 175, 0.2) 25%,
                rgba(42, 42, 42, 0.95) 50%,
                rgba(31, 31, 31, 0.98) 100%) !important;
            border-color: rgba(59, 130, 246, 0.3) !important;
            border-radius: 12px !important; /* 与白天主题保持一致 */
            /* 未选中状态的柔和蓝色氛围光 */
            box-shadow:
                0 0 35px rgba(59, 130, 246, 0.08),       /* 外围柔和光晕 */
                0 5px 20px rgba(59, 130, 246, 0.06),     /* 主要阴影 */
                0 2px 8px rgba(59, 130, 246, 0.04),      /* 细节阴影 */
                inset 0 1px 0 rgba(255, 255, 255, 0.03), /* 内部高光 */
                inset 0 -1px 0 rgba(59, 130, 246, 0.08), /* 内部蓝色阴影 */
                inset 1px 0 0 rgba(59, 130, 246, 0.04),  /* 左侧内光 */
                inset -1px 0 0 rgba(59, 130, 246, 0.04) !important; /* 右侧内光 */
        }

        /* 横屏比例下的图片圆角优化 */
        body.dark-theme #image-gen-result-container .image-display-frame.aspect-landscape img {
            border-radius: 10px; /* 比外框小2px */
            box-shadow:
                inset 0 0 0 1px rgba(59, 130, 246, 0.12),  /* 蓝色内边框 */
                inset 0 0 25px rgba(0, 0, 0, 0.25),        /* 内部阴影 */
                inset 0 0 50px rgba(30, 58, 138, 0.1);     /* 蓝色内部光晕 */
        }

        body.dark-theme #image-gen-result-container .image-display-frame.aspect-landscape:hover {
            border-color: rgba(59, 130, 246, 0.5) !important;
            /* 悬停时增强但保持自然的蓝色氛围光 */
            box-shadow:
                0 0 55px rgba(59, 130, 246, 0.15),       /* 增强外围光晕 */
                0 10px 35px rgba(59, 130, 246, 0.12),    /* 增强主要阴影 */
                0 4px 16px rgba(59, 130, 246, 0.08),     /* 增强细节阴影 */
                inset 0 1px 0 rgba(255, 255, 255, 0.05), /* 增强内部高光 */
                inset 0 -1px 0 rgba(59, 130, 246, 0.12), /* 增强内部蓝色阴影 */
                inset 1px 0 0 rgba(59, 130, 246, 0.06),  /* 增强左侧内光 */
                inset -1px 0 0 rgba(59, 130, 246, 0.06) !important; /* 增强右侧内光 */
        }

        /* 黑夜模式 - 现代移动端 (9:16竖屏) - 优化氛围光 */
        body.dark-theme #image-gen-result-container .image-display-frame.aspect-portrait {
            background: linear-gradient(145deg,
                rgba(88, 28, 135, 0.25) 0%,
                rgba(107, 33, 168, 0.2) 25%,
                rgba(42, 42, 42, 0.95) 50%,
                rgba(31, 31, 31, 0.98) 100%) !important;
            border-color: rgba(139, 92, 246, 0.3) !important;
            border-radius: 18px !important; /* 与白天主题保持一致 */
            /* 未选中状态的柔和紫色氛围光 */
            box-shadow:
                0 0 32px rgba(139, 92, 246, 0.08),       /* 外围柔和光晕 */
                0 6px 24px rgba(139, 92, 246, 0.06),     /* 主要阴影 */
                0 2px 8px rgba(139, 92, 246, 0.04),      /* 细节阴影 */
                inset 0 1px 0 rgba(255, 255, 255, 0.03), /* 内部高光 */
                inset 0 -1px 0 rgba(139, 92, 246, 0.08), /* 内部紫色阴影 */
                inset 1px 0 0 rgba(139, 92, 246, 0.04),  /* 左侧内光 */
                inset -1px 0 0 rgba(139, 92, 246, 0.04) !important; /* 右侧内光 */
        }

        /* 竖屏比例下的图片圆角优化 */
        body.dark-theme #image-gen-result-container .image-display-frame.aspect-portrait img {
            border-radius: 16px; /* 比外框小2px */
            box-shadow:
                inset 0 0 0 1px rgba(139, 92, 246, 0.12),  /* 紫色内边框 */
                inset 0 0 25px rgba(0, 0, 0, 0.25),        /* 内部阴影 */
                inset 0 0 50px rgba(88, 28, 135, 0.1);     /* 紫色内部光晕 */
        }

        body.dark-theme #image-gen-result-container .image-display-frame.aspect-portrait:hover {
            border-color: rgba(139, 92, 246, 0.5) !important;
            /* 悬停时增强但保持自然的紫色氛围光 */
            box-shadow:
                0 0 52px rgba(139, 92, 246, 0.15),       /* 增强外围光晕 */
                0 12px 40px rgba(139, 92, 246, 0.12),    /* 增强主要阴影 */
                0 4px 16px rgba(139, 92, 246, 0.08),     /* 增强细节阴影 */
                inset 0 1px 0 rgba(255, 255, 255, 0.05), /* 增强内部高光 */
                inset 0 -1px 0 rgba(139, 92, 246, 0.12), /* 增强内部紫色阴影 */
                inset 1px 0 0 rgba(139, 92, 246, 0.06),  /* 增强左侧内光 */
                inset -1px 0 0 rgba(139, 92, 246, 0.06) !important; /* 增强右侧内光 */
        }

        /* 黑夜模式 - 相机标准 (4:3标准) - 优化氛围光 */
        body.dark-theme #image-gen-result-container .image-display-frame.aspect-standard {
            background: linear-gradient(145deg,
                rgba(180, 83, 9, 0.25) 0%,
                rgba(217, 119, 6, 0.2) 25%,
                rgba(42, 42, 42, 0.95) 50%,
                rgba(31, 31, 31, 0.98) 100%) !important;
            border-color: rgba(245, 158, 11, 0.3) !important;
            /* 未选中状态的柔和琥珀色氛围光 */
            box-shadow:
                0 0 28px rgba(245, 158, 11, 0.08),       /* 外围柔和光晕 */
                0 5px 20px rgba(245, 158, 11, 0.06),     /* 主要阴影 */
                0 2px 8px rgba(245, 158, 11, 0.04),      /* 细节阴影 */
                inset 0 1px 0 rgba(255, 255, 255, 0.03), /* 内部高光 */
                inset 0 -1px 0 rgba(245, 158, 11, 0.08), /* 内部琥珀色阴影 */
                inset 1px 0 0 rgba(245, 158, 11, 0.04),  /* 左侧内光 */
                inset -1px 0 0 rgba(245, 158, 11, 0.04) !important; /* 右侧内光 */
        }

        body.dark-theme #image-gen-result-container .image-display-frame.aspect-standard:hover {
            border-color: rgba(245, 158, 11, 0.5) !important;
            /* 悬停时增强但保持自然的琥珀色氛围光 */
            box-shadow:
                0 0 48px rgba(245, 158, 11, 0.15),       /* 增强外围光晕 */
                0 10px 35px rgba(245, 158, 11, 0.12),    /* 增强主要阴影 */
                0 4px 16px rgba(245, 158, 11, 0.08),     /* 增强细节阴影 */
                inset 0 1px 0 rgba(255, 255, 255, 0.05), /* 增强内部高光 */
                inset 0 -1px 0 rgba(245, 158, 11, 0.12), /* 增强内部琥珀色阴影 */
                inset 1px 0 0 rgba(245, 158, 11, 0.06),  /* 增强左侧内光 */
                inset -1px 0 0 rgba(245, 158, 11, 0.06) !important; /* 增强右侧内光 */
        }

        /* 黑夜模式 - 海报竖版 (3:4海报) - 优化氛围光 */
        body.dark-theme #image-gen-result-container .image-display-frame.aspect-poster {
            background: linear-gradient(145deg,
                rgba(185, 28, 28, 0.25) 0%,
                rgba(220, 38, 38, 0.2) 25%,
                rgba(42, 42, 42, 0.95) 50%,
                rgba(31, 31, 31, 0.98) 100%) !important;
            border-color: rgba(239, 68, 68, 0.3) !important;
            /* 未选中状态的柔和红色氛围光 */
            box-shadow:
                0 0 30px rgba(239, 68, 68, 0.08),        /* 外围柔和光晕 */
                0 6px 24px rgba(239, 68, 68, 0.06),      /* 主要阴影 */
                0 2px 8px rgba(239, 68, 68, 0.04),       /* 细节阴影 */
                inset 0 1px 0 rgba(255, 255, 255, 0.03), /* 内部高光 */
                inset 0 -1px 0 rgba(239, 68, 68, 0.08),  /* 内部红色阴影 */
                inset 1px 0 0 rgba(239, 68, 68, 0.04),   /* 左侧内光 */
                inset -1px 0 0 rgba(239, 68, 68, 0.04) !important; /* 右侧内光 */
        }

        body.dark-theme #image-gen-result-container .image-display-frame.aspect-poster:hover {
            border-color: rgba(239, 68, 68, 0.5) !important;
            /* 悬停时增强但保持自然的红色氛围光 */
            box-shadow:
                0 0 50px rgba(239, 68, 68, 0.15),        /* 增强外围光晕 */
                0 12px 40px rgba(239, 68, 68, 0.12),     /* 增强主要阴影 */
                0 4px 16px rgba(239, 68, 68, 0.08),      /* 增强细节阴影 */
                inset 0 1px 0 rgba(255, 255, 255, 0.05), /* 增强内部高光 */
                inset 0 -1px 0 rgba(239, 68, 68, 0.12),  /* 增强内部红色阴影 */
                inset 1px 0 0 rgba(239, 68, 68, 0.06),   /* 增强左侧内光 */
                inset -1px 0 0 rgba(239, 68, 68, 0.06) !important; /* 增强右侧内光 */
        }

        /* 黑夜模式 - 电影大片 (21:9电影) - 优化氛围光 */
        body.dark-theme #image-gen-result-container .image-display-frame.aspect-cinema {
            background: linear-gradient(145deg,
                rgba(67, 56, 202, 0.25) 0%,
                rgba(79, 70, 229, 0.2) 25%,
                rgba(42, 42, 42, 0.95) 50%,
                rgba(31, 31, 31, 0.98) 100%) !important;
            border-color: rgba(99, 102, 241, 0.3) !important;
            /* 未选中状态的柔和靛蓝色氛围光 */
            box-shadow:
                0 0 36px rgba(99, 102, 241, 0.08),       /* 外围柔和光晕 */
                0 6px 28px rgba(99, 102, 241, 0.06),     /* 主要阴影 */
                0 2px 10px rgba(99, 102, 241, 0.04),     /* 细节阴影 */
                inset 0 1px 0 rgba(255, 255, 255, 0.03), /* 内部高光 */
                inset 0 -1px 0 rgba(99, 102, 241, 0.08), /* 内部靛蓝色阴影 */
                inset 1px 0 0 rgba(99, 102, 241, 0.04),  /* 左侧内光 */
                inset -1px 0 0 rgba(99, 102, 241, 0.04) !important; /* 右侧内光 */
        }

        body.dark-theme #image-gen-result-container .image-display-frame.aspect-cinema:hover {
            border-color: rgba(99, 102, 241, 0.5) !important;
            /* 悬停时增强但保持自然的靛蓝色氛围光 */
            box-shadow:
                0 0 60px rgba(99, 102, 241, 0.15),       /* 增强外围光晕 */
                0 15px 45px rgba(99, 102, 241, 0.12),    /* 增强主要阴影 */
                0 5px 20px rgba(99, 102, 241, 0.08),     /* 增强细节阴影 */
                inset 0 1px 0 rgba(255, 255, 255, 0.05), /* 增强内部高光 */
                inset 0 -1px 0 rgba(99, 102, 241, 0.12), /* 增强内部靛蓝色阴影 */
                inset 1px 0 0 rgba(99, 102, 241, 0.06),  /* 增强左侧内光 */
                inset -1px 0 0 rgba(99, 102, 241, 0.06) !important; /* 增强右侧内光 */
        }

        /* 黑夜模式 - 书籍封面 (2:3书籍) - 优化氛围光 */
        body.dark-theme #image-gen-result-container .image-display-frame.aspect-book {
            background: linear-gradient(145deg,
                rgba(88, 28, 135, 0.25) 0%,
                rgba(107, 33, 168, 0.2) 25%,
                rgba(42, 42, 42, 0.95) 50%,
                rgba(31, 31, 31, 0.98) 100%) !important;
            border-color: rgba(124, 58, 237, 0.3) !important;
            /* 未选中状态的柔和深紫色氛围光 */
            box-shadow:
                0 0 32px rgba(124, 58, 237, 0.08),       /* 外围柔和光晕 */
                0 6px 24px rgba(124, 58, 237, 0.06),     /* 主要阴影 */
                0 2px 8px rgba(124, 58, 237, 0.04),      /* 细节阴影 */
                inset 0 1px 0 rgba(255, 255, 255, 0.03), /* 内部高光 */
                inset 0 -1px 0 rgba(124, 58, 237, 0.08), /* 内部深紫色阴影 */
                inset 1px 0 0 rgba(124, 58, 237, 0.04),  /* 左侧内光 */
                inset -1px 0 0 rgba(124, 58, 237, 0.04) !important; /* 右侧内光 */
        }

        body.dark-theme #image-gen-result-container .image-display-frame.aspect-book:hover {
            border-color: rgba(124, 58, 237, 0.5) !important;
            /* 悬停时增强但保持自然的深紫色氛围光 */
            box-shadow:
                0 0 53px rgba(124, 58, 237, 0.15),       /* 增强外围光晕 */
                0 12px 40px rgba(124, 58, 237, 0.12),    /* 增强主要阴影 */
                0 4px 16px rgba(124, 58, 237, 0.08),     /* 增强细节阴影 */
                inset 0 1px 0 rgba(255, 255, 255, 0.05), /* 增强内部高光 */
                inset 0 -1px 0 rgba(124, 58, 237, 0.12), /* 增强内部深紫色阴影 */
                inset 1px 0 0 rgba(124, 58, 237, 0.06),  /* 增强左侧内光 */
                inset -1px 0 0 rgba(124, 58, 237, 0.06) !important; /* 增强右侧内光 */
        }

        /* 黑夜模式 - 风景摄影 (3:2摄影) - 优化氛围光 */
        body.dark-theme #image-gen-result-container .image-display-frame.aspect-photo {
            background: linear-gradient(145deg,
                rgba(8, 145, 178, 0.25) 0%,
                rgba(14, 165, 233, 0.2) 25%,
                rgba(42, 42, 42, 0.95) 50%,
                rgba(31, 31, 31, 0.98) 100%) !important;
            border-color: rgba(6, 182, 212, 0.3) !important;
            /* 未选中状态的柔和青色氛围光 */
            box-shadow:
                0 0 33px rgba(6, 182, 212, 0.08),        /* 外围柔和光晕 */
                0 6px 24px rgba(6, 182, 212, 0.06),      /* 主要阴影 */
                0 2px 8px rgba(6, 182, 212, 0.04),       /* 细节阴影 */
                inset 0 1px 0 rgba(255, 255, 255, 0.03), /* 内部高光 */
                inset 0 -1px 0 rgba(6, 182, 212, 0.08),  /* 内部青色阴影 */
                inset 1px 0 0 rgba(6, 182, 212, 0.04),   /* 左侧内光 */
                inset -1px 0 0 rgba(6, 182, 212, 0.04) !important; /* 右侧内光 */
        }

        body.dark-theme #image-gen-result-container .image-display-frame.aspect-photo:hover {
            border-color: rgba(6, 182, 212, 0.5) !important;
            /* 悬停时增强但保持自然的青色氛围光 */
            box-shadow:
                0 0 55px rgba(6, 182, 212, 0.15),        /* 增强外围光晕 */
                0 12px 40px rgba(6, 182, 212, 0.12),     /* 增强主要阴影 */
                0 4px 16px rgba(6, 182, 212, 0.08),      /* 增强细节阴影 */
                inset 0 1px 0 rgba(255, 255, 255, 0.05), /* 增强内部高光 */
                inset 0 -1px 0 rgba(6, 182, 212, 0.12),  /* 增强内部青色阴影 */
                inset 1px 0 0 rgba(6, 182, 212, 0.06),   /* 增强左侧内光 */
                inset -1px 0 0 rgba(6, 182, 212, 0.06) !important; /* 增强右侧内光 */
        }

        /* 黑夜模式 - 艺术画框 (5:4艺术) - 优化氛围光 */
        body.dark-theme #image-gen-result-container .image-display-frame.aspect-art {
            background: linear-gradient(145deg,
                rgba(6, 95, 70, 0.25) 0%,
                rgba(16, 185, 129, 0.2) 25%,
                rgba(42, 42, 42, 0.95) 50%,
                rgba(31, 31, 31, 0.98) 100%) !important;
            border-color: rgba(5, 150, 105, 0.3) !important;
            /* 未选中状态的柔和深绿色氛围光 */
            box-shadow:
                0 0 34px rgba(5, 150, 105, 0.08),        /* 外围柔和光晕 */
                0 7px 26px rgba(5, 150, 105, 0.06),      /* 主要阴影 */
                0 2px 10px rgba(5, 150, 105, 0.04),      /* 细节阴影 */
                inset 0 1px 0 rgba(255, 255, 255, 0.03), /* 内部高光 */
                inset 0 -1px 0 rgba(5, 150, 105, 0.08),  /* 内部深绿色阴影 */
                inset 1px 0 0 rgba(5, 150, 105, 0.04),   /* 左侧内光 */
                inset -1px 0 0 rgba(5, 150, 105, 0.04) !important; /* 右侧内光 */
        }

        body.dark-theme #image-gen-result-container .image-display-frame.aspect-art:hover {
            border-color: rgba(5, 150, 105, 0.5) !important;
            /* 悬停时增强但保持自然的深绿色氛围光 */
            box-shadow:
                0 0 58px rgba(5, 150, 105, 0.15),        /* 增强外围光晕 */
                0 14px 42px rgba(5, 150, 105, 0.12),     /* 增强主要阴影 */
                0 5px 18px rgba(5, 150, 105, 0.08),      /* 增强细节阴影 */
                inset 0 1px 0 rgba(255, 255, 255, 0.05), /* 增强内部高光 */
                inset 0 -1px 0 rgba(5, 150, 105, 0.12),  /* 增强内部深绿色阴影 */
                inset 1px 0 0 rgba(5, 150, 105, 0.06),   /* 增强左侧内光 */
                inset -1px 0 0 rgba(5, 150, 105, 0.06) !important; /* 增强右侧内光 */
        }

        /* 黑夜模式 - 自定义创意 (自定义比例) - 优化氛围光 */
        body.dark-theme #image-gen-result-container .image-display-frame.aspect-custom {
            background: linear-gradient(145deg,
                rgba(190, 24, 93, 0.25) 0%,
                rgba(219, 39, 119, 0.2) 25%,
                rgba(42, 42, 42, 0.95) 50%,
                rgba(31, 31, 31, 0.98) 100%) !important;
            border-color: rgba(236, 72, 153, 0.3) !important;
            /* 未选中状态的柔和粉色氛围光 */
            box-shadow:
                0 0 36px rgba(236, 72, 153, 0.08),       /* 外围柔和光晕 */
                0 7px 28px rgba(236, 72, 153, 0.06),     /* 主要阴影 */
                0 3px 12px rgba(236, 72, 153, 0.04),     /* 细节阴影 */
                inset 0 1px 0 rgba(255, 255, 255, 0.03), /* 内部高光 */
                inset 0 -1px 0 rgba(236, 72, 153, 0.08), /* 内部粉色阴影 */
                inset 1px 0 0 rgba(236, 72, 153, 0.04),  /* 左侧内光 */
                inset -1px 0 0 rgba(236, 72, 153, 0.04) !important; /* 右侧内光 */
        }

        body.dark-theme #image-gen-result-container .image-display-frame.aspect-custom:hover {
            border-color: rgba(236, 72, 153, 0.5) !important;
            /* 悬停时增强但保持自然的粉色氛围光 */
            box-shadow:
                0 0 62px rgba(236, 72, 153, 0.15),       /* 增强外围光晕 */
                0 16px 48px rgba(236, 72, 153, 0.12),    /* 增强主要阴影 */
                0 6px 22px rgba(236, 72, 153, 0.08),     /* 增强细节阴影 */
                inset 0 1px 0 rgba(255, 255, 255, 0.05), /* 增强内部高光 */
                inset 0 -1px 0 rgba(236, 72, 153, 0.12), /* 增强内部粉色阴影 */
                inset 1px 0 0 rgba(236, 72, 153, 0.06),  /* 增强左侧内光 */
                inset -1px 0 0 rgba(236, 72, 153, 0.06) !important; /* 增强右侧内光 */
        }

        /* === 暗黑主题下各比例图片圆角统一优化 === */

        /* 标准比例 (4:3) 圆角优化 */
        body.dark-theme #image-gen-result-container .image-display-frame.aspect-standard {
            border-radius: 16px !important;
        }
        body.dark-theme #image-gen-result-container .image-display-frame.aspect-standard img {
            border-radius: 14px;
            box-shadow:
                inset 0 0 0 1px rgba(245, 158, 11, 0.12),
                inset 0 0 25px rgba(0, 0, 0, 0.25),
                inset 0 0 50px rgba(180, 83, 9, 0.1);
        }

        /* 海报比例 (3:4) 圆角优化 */
        body.dark-theme #image-gen-result-container .image-display-frame.aspect-poster {
            border-radius: 18px !important;
        }
        body.dark-theme #image-gen-result-container .image-display-frame.aspect-poster img {
            border-radius: 16px;
            box-shadow:
                inset 0 0 0 1px rgba(239, 68, 68, 0.12),
                inset 0 0 25px rgba(0, 0, 0, 0.25),
                inset 0 0 50px rgba(185, 28, 28, 0.1);
        }

        /* 电影比例 (21:9) 圆角优化 */
        body.dark-theme #image-gen-result-container .image-display-frame.aspect-cinema {
            border-radius: 10px !important;
        }
        body.dark-theme #image-gen-result-container .image-display-frame.aspect-cinema img {
            border-radius: 8px;
            box-shadow:
                inset 0 0 0 1px rgba(99, 102, 241, 0.12),
                inset 0 0 25px rgba(0, 0, 0, 0.25),
                inset 0 0 50px rgba(67, 56, 202, 0.1);
        }

        /* 书籍比例 (2:3) 圆角优化 */
        body.dark-theme #image-gen-result-container .image-display-frame.aspect-book {
            border-radius: 22px !important;
        }
        body.dark-theme #image-gen-result-container .image-display-frame.aspect-book img {
            border-radius: 20px;
            box-shadow:
                inset 0 0 0 1px rgba(124, 58, 237, 0.12),
                inset 0 0 25px rgba(0, 0, 0, 0.25),
                inset 0 0 50px rgba(88, 28, 135, 0.1);
        }

        /* 摄影比例 (3:2) 圆角优化 */
        body.dark-theme #image-gen-result-container .image-display-frame.aspect-photo {
            border-radius: 14px !important;
        }
        body.dark-theme #image-gen-result-container .image-display-frame.aspect-photo img {
            border-radius: 12px;
            box-shadow:
                inset 0 0 0 1px rgba(6, 182, 212, 0.12),
                inset 0 0 25px rgba(0, 0, 0, 0.25),
                inset 0 0 50px rgba(8, 145, 178, 0.1);
        }

        /* 艺术比例 (5:4) 圆角优化 */
        body.dark-theme #image-gen-result-container .image-display-frame.aspect-art {
            border-radius: 25px !important;
        }
        body.dark-theme #image-gen-result-container .image-display-frame.aspect-art img {
            border-radius: 23px;
            box-shadow:
                inset 0 0 0 1px rgba(5, 150, 105, 0.12),
                inset 0 0 25px rgba(0, 0, 0, 0.25),
                inset 0 0 50px rgba(6, 95, 70, 0.1);
        }

        /* 自定义比例圆角优化 */
        body.dark-theme #image-gen-result-container .image-display-frame.aspect-custom {
            border-radius: 28px !important;
        }
        body.dark-theme #image-gen-result-container .image-display-frame.aspect-custom img {
            border-radius: 26px;
            box-shadow:
                inset 0 0 0 1px rgba(236, 72, 153, 0.12),
                inset 0 0 25px rgba(0, 0, 0, 0.25),
                inset 0 0 50px rgba(190, 24, 93, 0.1);
        }

        body.dark-theme .image-preview-frame.loading,
        body.dark-theme .batch-image-item.loading {
            background:
                radial-gradient(circle at 25% 25%, rgba(100, 181, 246, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(100, 181, 246, 0.15) 0%, transparent 50%),
                linear-gradient(45deg, rgba(100, 181, 246, 0.08) 25%, transparent 25%),
                linear-gradient(-45deg, rgba(100, 181, 246, 0.08) 25%, transparent 25%),
                linear-gradient(45deg, transparent 75%, rgba(100, 181, 246, 0.08) 75%),
                linear-gradient(-45deg, transparent 75%, rgba(100, 181, 246, 0.08) 75%),
                #2a2a2a;
            background-size: 32px 32px, 32px 32px, 16px 16px, 16px 16px, 16px 16px, 16px 16px;
            animation:
                dashPulseDark 3s ease-in-out infinite,
                previewLoading 2s linear infinite;
        }

        body.dark-theme .batch-progress-bar {
            background: #333;
        }

        body.dark-theme .batch-progress-fill {
            background: linear-gradient(90deg, #64b5f6, #42a5f5);
        }

        /* 精巧细腻的输入框样式优化 */
        .image-gen-referrer-container {
            max-width: 300px;
            flex: 1;
            margin-bottom: 8px;
            padding-bottom: 4px;
        }

        .image-gen-batch-container {
            max-width: 150px;
            min-width: 120px;
            margin-bottom: 8px;
            padding-bottom: 4px;
        }

        #image-gen-referrer-input {
            width: 100%;
            max-width: 280px;
        }

        #image-gen-batch-count-input {
            width: 80px;
            text-align: center;
            padding: 8px 12px;
        }

        /* 种子输入框优化 */
        .image-gen-seed-container input[type="number"] {
            max-width: 140px;
        }

        /* 尺寸输入框优化 */
        .image-gen-dimension-container input[type="number"] {
            max-width: 100px;
        }

        /* 模型选择器优化 */
        .image-gen-model-container {
            max-width: 200px;
            flex: 1;
        }

        /* 宽高比选择器优化 */
        .image-gen-aspect-container {
            max-width: 180px;
            flex: 1;
        }

        /* 绘画模块输入框聚焦样式已统一到全局聚焦效果系统中 */
        /* 移除专用样式，使用全局统一的 .modern-focus 效果 */

        #image-gen-progress { display: flex; align-items: center; justify-content: center; gap: 8px; margin-top: 15px; font-size: 0.95em; color: #495057; }
        #image-gen-progress i { font-size: 1.2em; }



        /* === 智能自适应图片查看器 === */
        #image-viewer-modal {
            display: none;
            position: fixed;
            inset: 0;
            background: rgba(0, 0, 0, 0.9);
            backdrop-filter: blur(8px);
            z-index: 1080;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            padding: 80px 40px 40px 40px;
        }

        #image-viewer-modal.visible {
            display: flex;
            opacity: 1;
        }

        #image-viewer-content {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: default;
            max-width: 100%;
            max-height: 100%;
        }

        #image-viewer-content img {
            display: block;
            max-width: min(90vw, 1200px);
            max-height: min(80vh, 800px);
            width: auto;
            height: auto;
            object-fit: contain;
            border-radius: 12px;
            box-shadow:
                0 25px 50px rgba(0, 0, 0, 0.5),
                0 0 0 1px rgba(255, 255, 255, 0.1);
            transition: transform 0.3s ease;
        }

        #image-viewer-content img:hover {
            transform: scale(1.02);
        }

        /* 响应式图片尺寸 */
        @media (max-width: 768px) {
            #image-viewer-content img {
                max-width: 95vw;
                max-height: 70vh;
            }
        }

        /* === 图片元数据悬停显示面板 === */
        .image-metadata-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(
                to top,
                rgba(0, 0, 0, 0.92) 0%,
                rgba(0, 0, 0, 0.75) 60%,
                rgba(0, 0, 0, 0.3) 85%,
                transparent 100%
            );
            backdrop-filter: blur(12px) saturate(1.2);
            -webkit-backdrop-filter: blur(12px) saturate(1.2);
            color: white;
            padding: 24px 28px 20px 28px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(25px);
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            border-radius: 0 0 16px 16px;
            pointer-events: none;
            box-shadow: 0 -8px 32px rgba(0, 0, 0, 0.3);
        }

        /* 图片元数据显示现在由JavaScript控制，移除CSS悬停效果 */
        /*
        #image-viewer-img:hover ~ .image-metadata-overlay,
        .image-metadata-overlay:hover {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }
        */

        .metadata-content {
            display: flex;
            flex-direction: column;
            gap: 12px;
            max-width: 100%;
        }

        .metadata-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 8px;
            padding-bottom: 8px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.15);
        }

        .metadata-header-icon {
            font-size: 16px;
            color: #60a5fa;
            text-shadow: 0 0 8px rgba(96, 165, 250, 0.3);
        }

        .metadata-header-title {
            font-size: 15px;
            font-weight: 700;
            color: rgba(255, 255, 255, 0.95);
            letter-spacing: 0.5px;
        }

        .metadata-row {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            font-size: 13px;
            line-height: 1.5;
            padding: 4px 0;
            transition: all 0.2s ease;
        }

        .metadata-row:hover {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 6px;
            padding: 6px 8px;
            margin: 2px -8px;
        }

        .metadata-icon {
            font-size: 14px;
            color: #94a3b8;
            min-width: 18px;
            text-align: center;
            margin-top: 1px;
            transition: color 0.2s ease;
        }

        .metadata-row:hover .metadata-icon {
            color: #60a5fa;
        }

        .metadata-label {
            font-weight: 600;
            color: rgba(255, 255, 255, 0.9);
            min-width: 70px;
            flex-shrink: 0;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .metadata-value {
            color: rgba(255, 255, 255, 0.85);
            word-break: break-word;
            flex: 1;
            font-weight: 400;
            line-height: 1.4;
        }

        .metadata-value.highlight {
            color: #60a5fa;
            font-weight: 600;
            text-shadow: 0 0 8px rgba(96, 165, 250, 0.2);
            background: linear-gradient(135deg, #60a5fa, #3b82f6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .image-metadata-overlay {
                padding: 20px 24px 16px 24px;
            }

            .metadata-header {
                margin-bottom: 6px;
            }

            .metadata-header-icon {
                font-size: 15px;
            }

            .metadata-header-title {
                font-size: 14px;
            }

            .metadata-row {
                font-size: 12px;
                gap: 10px;
            }

            .metadata-icon {
                font-size: 13px;
                min-width: 16px;
            }

            .metadata-label {
                min-width: 60px;
                font-size: 11px;
            }
        }

        @media (max-width: 480px) {
            #image-viewer-modal {
                padding: 60px 20px 20px 20px;
            }

            #image-viewer-content img {
                max-width: 98vw;
                max-height: 65vh;
                border-radius: 8px;
            }

            .image-metadata-overlay {
                padding: 16px 20px 12px 20px;
            }

            .metadata-header {
                margin-bottom: 4px;
                padding-bottom: 6px;
            }

            .metadata-header-icon {
                font-size: 14px;
            }

            .metadata-header-title {
                font-size: 13px;
            }

            .metadata-row {
                font-size: 11px;
                gap: 8px;
                flex-direction: column;
                align-items: flex-start;
                padding: 6px 0;
            }

            .metadata-row:hover {
                padding: 8px 6px;
                margin: 2px -6px;
            }

            .metadata-icon {
                font-size: 12px;
                min-width: 14px;
                margin-bottom: 2px;
            }

            .metadata-label {
                min-width: auto;
                margin-bottom: 3px;
                font-size: 10px;
                display: flex;
                align-items: center;
                gap: 6px;
            }

            .metadata-value {
                margin-left: 20px;
                font-size: 11px;
            }
        }

        /* 超大屏幕优化 */
        @media (min-width: 1920px) {
            #image-viewer-content img {
                max-width: 70vw;
                max-height: 85vh;
            }
        }

        /* === 现代化图片查看器操作按钮 === */
        .image-viewer-actions {
            position: fixed;
            top: 24px;
            right: 24px;
            display: flex;
            gap: 8px;
            z-index: 1081;
        }

        .image-viewer-action-button {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            padding: 8px 16px;
            border: 0.5px solid transparent;
            border-radius: 6px;
            font-size: 13px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            min-width: 40px;
            height: 36px;
            line-height: 20px;
        }

        .image-viewer-action-button:hover {
            transform: translateY(-0.5px);
        }

        .image-viewer-action-button:active {
            transform: translateY(0);
        }

        .image-viewer-action-button.save {
            background: var(--color-success);
            color: white;
            border-color: var(--color-success);
        }

        .image-viewer-action-button.save:hover {
            background: var(--color-success-hover);
            border-color: var(--color-success-hover);
        }

        .image-viewer-action-button.close {
            background: var(--color-danger);
            color: white;
            border-color: var(--color-danger);
        }

        .image-viewer-action-button.close:hover {
            background: var(--color-danger-hover);
            border-color: var(--color-danger-hover);
        }

        .image-viewer-action-button i {
            font-size: 1em;
        }

        .image-viewer-action-button span {
            font-weight: 500;
            letter-spacing: 0.2px;
        }

        /* 图片查看器按钮聚焦框修复 */
        .image-viewer-action-button:focus:not(:focus-visible),
        .image-viewer-nav-button:focus:not(:focus-visible) {
            outline: none !important;
            box-shadow:
                0 4px 12px rgba(0, 0, 0, 0.15),
                0 0 0 1px rgba(255, 255, 255, 0.05) !important;
        }

        .image-viewer-action-button:focus-visible,
        .image-viewer-nav-button:focus-visible {
            outline: none !important;
            border-color: var(--focus-border-color) !important;
            box-shadow: 0 0 0 var(--focus-glow-radius) var(--focus-glow-color) !important;
            transition: var(--focus-transition) !important;
        }

        /* === 图片查看器翻页功能 === */

        /* 翻页按钮样式 */
        .image-viewer-nav-button {
            position: fixed;
            top: 50%;
            transform: translateY(-50%);
            width: 50px;
            height: 50px;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(12px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            color: #374151;
            font-size: 1.2em;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 1082;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .image-viewer-nav-button:hover {
            background: rgba(255, 255, 255, 1);
            transform: translateY(-50%) scale(1.05);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
        }

        .image-viewer-nav-button:active {
            transform: translateY(-50%) scale(0.98);
        }

        .image-viewer-nav-button.prev {
            left: 30px;
        }

        .image-viewer-nav-button.next {
            right: 30px;
        }



        /* 图片信息显示 */
        .image-viewer-info {
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(12px);
            color: #ffffff;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 500;
            z-index: 1082;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        /* 移动端翻页按钮优化 */
        @media (max-width: 768px) {
            .image-viewer-nav-button {
                width: 44px;
                height: 44px;
                font-size: 1em;
            }

            .image-viewer-nav-button.prev {
                left: 20px;
            }

            .image-viewer-nav-button.next {
                right: 20px;
            }

            .image-viewer-info {
                bottom: 20px;
                font-size: 0.8em;
                padding: 6px 12px;
            }
        }

        /* 触摸设备优化 */
        @media (hover: none) and (pointer: coarse) {
            /* 增大触摸目标 */
            .image-gen-selector .custom-selector-trigger,
            .image-gen-action-button,
            .image-viewer-action-button {
                min-height: 44px;
                touch-action: manipulation;
            }

            /* 触摸反馈 */
            .image-viewer-action-button:active {
                transform: scale(0.96) !important;
            }

            .image-gen-action-button:active {
                transform: scale(0.98);
            }

            /* 防止文本选择 */
            .image-viewer-actions,
            .image-gen-card {
                -webkit-user-select: none;
                user-select: none;
            }
        }

        /* --- Backend Log Modal (Modern Design) --- */
        #backend-log-modal { display: none; position: fixed; inset: 0; background-color: rgba(0, 0, 0, 0.75); z-index: 1070; align-items: center; justify-content: center; opacity: 0; transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out; transform: scale(0.95); padding: 20px; backdrop-filter: blur(6px); }
    @keyframes loadingPulse { 0% { opacity: 0.6; } 50% { opacity: 1; } 100% { opacity: 0.6; } }
    .loading-text { animation: loadingPulse 2.5s infinite ease-in-out; }
        #backend-log-modal.visible { display: flex; opacity: 1; transform: scale(1); }
        .backend-log-modal-content { background-color: #fff; padding: 30px; border-radius: 16px; box-shadow: 0 10px 40px rgba(0,0,0,0.2); width: 88%; max-width: 1100px; height: 80%; display: flex; flex-direction: column; position: relative; border: 1px solid rgba(0,0,0,0.08); }
        .backend-log-modal-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; padding-bottom: 15px; border-bottom: 1px solid #e9ecef; flex-shrink: 0; }
        .backend-log-modal-header h2 { margin: 0; font-size: 1.5em; color: #212529; font-weight: 600; display: flex; align-items: center; gap: 10px; }
        .backend-log-modal-header h2 i { color: #3b82f6; }
        .backend-log-modal-actions { display: flex; gap: 12px; }
        .backend-log-modal-actions button { background-color: #3b82f6; color: white; border: none; padding: 10px 18px; border-radius: 8px; cursor: pointer; font-size: 1em; transition: all 0.2s ease; display: flex; align-items: center; gap: 8px; box-shadow: 0 2px 5px rgba(59,130,246,0.25); }
        .backend-log-modal-actions button:hover { background-color: #2563eb; transform: translateY(-2px); box-shadow: 0 5px 10px rgba(59,130,246,0.3); }
        .backend-log-modal-actions button:active { transform: translateY(0); box-shadow: 0 2px 5px rgba(59,130,246,0.25); }
        .backend-log-modal-actions button#close-backend-log-modal { background-color: #6b7280; box-shadow: 0 2px 5px rgba(107,114,128,0.25); }
        .backend-log-modal-actions button#close-backend-log-modal:hover { background-color: #4b5563; box-shadow: 0 5px 10px rgba(107,114,128,0.3); }
        #backend-log-display { flex-grow: 1; background-color: #f9fafb; border: 1px solid #e5e7eb; border-radius: 12px; padding: 22px; overflow-y: auto; white-space: pre-wrap; word-wrap: break-word; font-family: 'Consolas', 'Monaco', 'Courier New', monospace; font-size: 0.95em; color: #374151; line-height: 1.8; box-shadow: inset 0 2px 6px rgba(0,0,0,0.06); transition: all 0.3s ease; }
        /* 后端日志显示滚动条隐藏 - 已通过全局设置处理 */
        #backend-log-display pre { margin: 0; padding: 0; font-family: inherit; }

        /* === 图形视图和代码视图互斥显示控制 === */

        /* 默认状态：显示图形，隐藏代码 */
        .mermaid-container:not(.code-view-active) .mermaid-diagram-view,
        .svg-container:not(.code-view-active) .svg-diagram-view {
            display: block !important;
        }

        .mermaid-container:not(.code-view-active) .mermaid-code-view,
        .svg-container:not(.code-view-active) .svg-code-view {
            display: none !important;
        }

        /* 代码视图激活状态：隐藏图形，显示代码 */
        .mermaid-container.code-view-active .mermaid-diagram-view,
        .svg-container.code-view-active .svg-diagram-view {
            display: none !important;
        }

        .mermaid-container.code-view-active .mermaid-code-view,
        .svg-container.code-view-active .svg-code-view {
            display: flex !important;
        }

        /* 强制隐藏状态 */
        .mermaid-diagram-view.hidden,
        .svg-diagram-view.hidden,
        .mermaid-code-view:not(.visible),
        .svg-code-view:not(.visible) {
            display: none !important;
        }

        /* Helper class */
        .hidden { display: none !important; }

        /* === 响应式媒体查询 === */

        /* 超小屏幕 (手机竖屏) */
        @media (max-width: 479px) {
            html {
                font-size: 12px;
            }

            #top-bar {
                height: 50px;
                padding: 0 var(--spacing-sm);
            }

            /* === 移动端专业级响应式设计 === */

            /* 模态框移动端优化 */
            #image-gen-modal .modal-content {
                width: 96%;
                max-width: none;
                margin: 8px;
                max-height: 94vh;
                border-radius: 12px;
            }

            #image-gen-modal .modal-form-area {
                padding: 16px;
            }

            /* 卡片布局移动端优化 */
            .image-gen-card {
                padding: 12px;
                margin-bottom: 12px;
            }

            .image-gen-card-title {
                font-size: 0.8em;
                margin-bottom: 10px;
            }

            /* 网格布局移动端调整 */
            .image-gen-grid.basic-params {
                grid-template-columns: 1fr;
                gap: 12px;
            }

            .image-gen-grid.size-params {
                grid-template-columns: 1fr 1fr;
                gap: 12px;
            }

            .image-gen-grid.advanced-params {
                grid-template-columns: 1fr;
                gap: 12px;
            }

            .image-gen-grid.toggle-params {
                grid-template-columns: 1fr;
                gap: 10px;
            }

            /* 表单组件移动端优化 */
            .image-gen-input {
                height: 44px;
                font-size: 16px; /* 防止iOS缩放 */
            }

            .image-gen-input[type="number"] {
                width: 100%;
                max-width: none;
            }

            .image-gen-input[type="text"] {
                max-width: none;
            }

            .image-gen-selector {
                max-width: none;
            }

            .image-gen-selector .custom-selector-trigger {
                height: 44px;
                max-width: none;
            }

            /* 切换开关移动端优化 */
            .image-gen-toggle-group {
                min-height: 44px;
                padding: 12px 16px;
            }

            .image-gen-toggle-group input[type="checkbox"] {
                transform: scale(1.3);
            }

            /* 按钮移动端优化 */
            .image-gen-action-button {
                height: 48px;
                font-size: 0.95em;
            }

            /* 批量生成移动端优化 */
            #image-gen-batch-preview-container {
                padding: 16px;
                margin-top: 16px;
            }

            .image-batch-grid[data-count="1"],
            .image-batch-grid[data-count="2"] {
                grid-template-columns: 1fr;
            }

            .image-batch-grid[data-count="3"],
            .image-batch-grid[data-count="4"],
            .image-batch-grid[data-count="5"],
            .image-batch-grid[data-count="6"] {
                grid-template-columns: repeat(2, 1fr);
            }

            .image-batch-grid[data-count="7"],
            .image-batch-grid[data-count="8"],
            .image-batch-grid[data-count="9"],
            .image-batch-grid[data-count="10"],
            .image-batch-grid[data-count="11"],
            .image-batch-grid[data-count="12"],
            .image-batch-grid[data-count="13"],
            .image-batch-grid[data-count="14"],
            .image-batch-grid[data-count="15"],
            .image-batch-grid[data-count="16"] {
                grid-template-columns: repeat(2, 1fr);
                gap: 12px;
            }

            /* 移动端批量图片项优化 */
            .batch-image-item {
                border-radius: 6px;
            }

            /* 图片查看器移动端优化 */
            .image-viewer-actions {
                top: 16px;
                right: 16px;
                gap: 6px;
            }

            .image-viewer-action-button {
                min-width: 40px;
                height: 40px;
                padding: 10px 12px;
            }

            .image-viewer-action-button span {
                display: none; /* 移动端只显示图标 */
            }

            /* 消息中图片移动端优化 */
            .message-content .generated-image-container {
                max-width: 250px;
            }

            .message-content .generated-image-container img {
                max-height: 250px;
            }

            /* 图片操作按钮移动端优化 - 仅用于生成的图片 */
            .message-content .generated-image-container .image-action-buttons {
                top: -28px;
                right: 2px;
                padding: 3px 6px;
            }

            .message-content .generated-image-container .image-action-button {
                width: 28px;
                height: 28px;
                font-size: 0.8em;
            }

            /* 图片查看器移动端优化 */
            #image-viewer-modal {
                padding: 80px 15px 15px 15px;
            }

            #image-viewer-content {
                max-width: calc(100vw - 30px);
                max-height: calc(100vh - 140px);
            }

            /* 图片查看器移动端优化 */
            .image-viewer-actions {
                top: 15px;
                right: 15px;
                padding: 8px 12px;
                gap: 8px;
            }

            .image-viewer-action-button {
                padding: 6px 10px;
                font-size: 0.8em;
                min-width: 36px;
                height: 32px;
            }

            .image-viewer-action-button span {
                display: none; /* 在小屏幕上只显示图标 */
            }

            /* 单张生图区域移动端优化 */
            #image-gen-result-container,
            #image-gen-preview-container {
                margin-top: 16px;
                padding: 16px;
            }

            #image-gen-result-container .result-header,
            #image-gen-preview-container .preview-header {
                margin-bottom: 12px;
                font-size: 0.85em;
            }

            .image-preview-frame {
                min-width: 150px;
                min-height: 120px;
                max-height: 50vh;
                border-radius: 8px;
                /* 移动端确保预览框与查看器比例一致 */
                max-width: 90vw;
            }

            .preview-loading {
                gap: 8px;
                font-size: 0.8em;
            }

            #image-gen-result-container .image-display-frame {
                border-radius: 8px;
            }

            .preview-loading i {
                font-size: 1.5em;
            }

            #chat-title {
                font-size: var(--font-size-base);
                padding: 0 var(--spacing-sm);
            }

            #menu-toggle-button,
            .top-action-button {
                width: 30px;
                height: 30px;
            }

            #conversation-options-bar {
                height: 44px;
                padding: 0 var(--spacing-sm);
                gap: var(--spacing-sm);
            }

            .custom-selector-trigger {
                height: 34px;
                font-size: var(--font-size-xs);
            }

            #sidebar {
                width: 100%;
                max-width: 100%;
            }

            #chatbox {
                padding: var(--spacing-lg) var(--spacing-sm);
            }

            .message-bubble-wrapper {
                max-width: 95%;
            }

            .message-bubble {
                max-width: 100%;
                margin: var(--spacing-xs) 0;
            }

            #input-container {
                padding: var(--spacing-sm);
            }

            #message-input {
                padding: var(--spacing-sm) 40px var(--spacing-sm) var(--spacing-sm);
                min-height: 40px;
                font-size: var(--font-size-sm);
            }

            #send-button {
                width: 30px;
                height: 30px;
                right: 4px;
                bottom: 4px;
            }

            .scroll-buttons-container {
                bottom: 100px;
                right: var(--spacing-sm);
            }

            .scroll-button {
                width: 34px;
                height: 34px;
            }

            #message-search-container {
                right: var(--spacing-sm);
                padding: var(--spacing-xs) var(--spacing-sm);
            }

            #message-search-input {
                width: 120px;
            }
        }

        /* 小屏幕 (手机横屏/小平板) */
        @media (min-width: 480px) and (max-width: 639px) {
            html {
                font-size: 13px;
            }

            #sidebar {
                width: 320px;
                max-width: 90%;
            }

            /* 小屏幕微调（已在移动端统一处理） */

            /* 图片查看器小屏幕微调 */
            .image-viewer-actions {
                top: 18px;
                right: 18px;
                padding: 7px 11px;
            }

            .image-viewer-action-button {
                min-width: 38px;
                height: 34px;
            }

            #chatbox {
                padding: var(--spacing-xl) var(--spacing-md);
            }

            .message-bubble-wrapper {
                max-width: 90%;
            }

            .message-bubble {
                max-width: 100%;
            }

            #input-container {
                padding: var(--spacing-md) var(--spacing-lg);
            }
        }

        /* 中等屏幕 (平板) */
        @media (min-width: 640px) and (max-width: 767px) {
            #sidebar {
                width: 300px;
                max-width: 85%;
            }

            .message-bubble-wrapper {
                max-width: 88%;
            }

            /* 平板微调（已在移动端统一处理） */

            /* 图片查看器平板恢复桌面端位置 */
            .image-viewer-actions {
                top: 20px;
                right: 20px;
            }

            .message-bubble {
                max-width: 100%;
            }

            #chatbox {
                padding: var(--spacing-xl) var(--spacing-lg);
            }
        }

        /* 大屏幕 (桌面) */
        @media (min-width: 768px) and (max-width: 1023px) {
            #sidebar {
                width: 280px;
                max-width: 80%;
            }

            .message-bubble-wrapper {
                max-width: 88%;
            }

            .message-bubble {
                max-width: 100%;
            }
        }

        /* 超大屏幕 (大桌面) */
        @media (min-width: 1024px) and (max-width: 1279px) {
            #sidebar {
                width: 280px;
                max-width: 75%;
            }

            .message-bubble-wrapper {
                max-width: 85%;
            }

            .message-bubble {
                max-width: 100%;
            }

            #chatbox {
                padding: var(--spacing-xl) var(--spacing-2xl);
            }
        }

        /* 极大屏幕 (4K显示器等) */
        @media (min-width: 1280px) {
            html {
                font-size: 15px;
            }

            #sidebar {
                width: 320px;
                max-width: 70%;
            }

            .message-bubble-wrapper {
                max-width: 82%;
            }

            .message-bubble {
                max-width: 100%;
            }

            #chatbox {
                padding: var(--spacing-2xl) var(--spacing-3xl);
            }

            #top-bar {
                height: 60px;
            }

            #conversation-options-bar {
                height: 48px; /* 统一高度，避免主题切换时的布局跳跃 */
            }
        }

        /* 横屏优化 */
        @media (orientation: landscape) and (max-height: 600px) {
            #top-bar {
                height: 48px;
            }

            #conversation-options-bar {
                height: 42px;
            }

            #chatbox {
                padding: var(--spacing-md);
            }

            #input-container {
                padding: var(--spacing-sm) var(--spacing-md);
            }

            .scroll-buttons-container {
                bottom: 80px;
            }
        }

        /* 高分辨率屏幕优化 - 边角渲染优化 */
        @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
            .scroll-button,
            .top-action-button,
            #menu-toggle-button,
            #send-button {
                border-width: 0.5px;
            }

            /* === 高分辨率输入框边角渲染优化 === */
            input:focus:not(#message-input),
            textarea:focus:not(#message-input),
            select:focus,
            .form-control:focus {
                /* 高分辨率屏幕下的边框优化 */
                border-width: 1px !important;
                /* 优化阴影渲染，避免锯齿 */
                box-shadow:
                    0 0 0 1.5px rgba(100, 181, 246, 0.2),
                    0 2px 6px rgba(0, 0, 0, 0.06) !important;
                /* 强制硬件加速，优化渲染 */
                transform: translateZ(0);
                /* 优化边框渲染 */
                -webkit-backface-visibility: hidden;
                backface-visibility: hidden;
                /* 抗锯齿优化 */
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale;
            }

            /* 消息输入框容器的高分辨率优化 - 单独处理避免冲突 */
            #input-textarea-wrapper:focus-within {
                /* 强制硬件加速，优化渲染 */
                transform: translateZ(0);
                /* 优化边框渲染 */
                -webkit-backface-visibility: hidden;
                backface-visibility: hidden;
                /* 抗锯齿优化 */
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale;
            }

            /* === 移动端滚动条优化 === */
            /* 移动端仅主聊天区域保留滚动条 */
            #chatbox::-webkit-scrollbar {
                width: 10px;
            }
        }

        /* 减少动画的用户偏好 */
        @media (prefers-reduced-motion: reduce) {
            * {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
                scroll-behavior: auto !important;
            }
        }

        /* === 浏览器特定字体渲染优化 === */

        /* Chrome/Safari 字体渲染优化 */
        @supports (-webkit-appearance: none) {
            .code-block-header,
            .code-block-footer,
            .code-action-button,
            .language-tag {
                -webkit-font-smoothing: antialiased;
                -webkit-text-stroke: 0.01em transparent;
            }
        }

        /* Firefox 字体渲染优化 */
        @-moz-document url-prefix() {
            .code-block-header,
            .code-block-footer,
            .code-action-button,
            .language-tag {
                -moz-osx-font-smoothing: grayscale;
                text-shadow: 0 0 1px transparent;
            }
        }

        /* Edge 字体渲染优化 */
        @supports (-ms-ime-align: auto) {
            .code-block-header,
            .code-block-footer,
            .code-action-button,
            .language-tag {
                text-rendering: geometricPrecision;
                font-feature-settings: "liga" 1, "kern" 1;
            }
        }

        /* 打印样式 */
        @media print {
            #sidebar,
            #top-bar,
            #conversation-options-bar,
            #input-container,
            .scroll-buttons-container,
            #message-search-container {
                display: none !important;
            }

            #main-content {
                margin: 0;
                padding: 0;
            }

            #chatbox {
                padding: 0;
                overflow: visible;
            }

            .message-bubble {
                break-inside: avoid;
                box-shadow: none;
                border: 1px solid #ccc;
                margin: var(--spacing-sm) 0;
            }

            .message-content {
                color: #000 !important;
                background: transparent !important;
            }
        }

        /* === 全局按钮聚焦框修复 === */

        /* 移除所有按钮鼠标点击后的聚焦框，保持键盘导航可访问性 */
        button:focus:not(:focus-visible),
        input[type="button"]:focus:not(:focus-visible),
        input[type="submit"]:focus:not(:focus-visible),
        input[type="reset"]:focus:not(:focus-visible),
        .btn:focus:not(:focus-visible),
        .button:focus:not(:focus-visible) {
            outline: none !important;
            box-shadow: none !important;
            border: inherit !important;
        }

        /* 保持键盘导航的可访问性 - 现代化聚焦效果 */
        button:focus-visible,
        input[type="button"]:focus-visible,
        input[type="submit"]:focus-visible,
        input[type="reset"]:focus-visible,
        .btn:focus-visible,
        .button:focus-visible {
            outline: none !important;
            border-color: var(--focus-border-color) !important;
            box-shadow: 0 0 0 var(--focus-glow-radius) var(--focus-glow-color) !important;
            transition: var(--focus-transition) !important;
        }

        /* === 零边界呼吸感设计优化 === */

        /* === 现代化输入框聚焦效果 - 已在基础样式中定义 === */
        /* 此部分已被统一聚焦效果覆盖，保持注释以供参考 */

        /* 去除呼吸感动画 - 保持简洁 */

        @keyframes focus-pulse {
            0%, 100% {
                box-shadow: 0 0 0 4px rgba(168, 197, 240, 0.08);
            }
            50% {
                box-shadow: 0 0 0 6px rgba(168, 197, 240, 0.05);
            }
        }

        /* 消息容器间距优化 - 增强呼吸感 */
        .message-container {
            margin-bottom: calc(var(--spacing-md) * 1.5);  /* 增加消息间距 */
        }

        /* 思考容器头部优化 */
        .thinking-process-header {
            background-color: #f1f3f4;  /* 更浅的灰色 */
            border-bottom: 1px solid rgba(0,0,0,0.05);  /* 极淡的分割线 */
        }

        /* 输入框优化 - 零边界风格 */
        #user-input {
            border: 1px solid rgba(0,0,0,0.08) !important;  /* 极淡的边框 */
            background: rgba(255,255,255,0.8) !important;   /* 半透明背景 */
        }

        #user-input:focus {
            border-color: rgba(0,0,0,0.08) !important;  /* 保持原始边框颜色 */
            background: rgba(255,255,255,0.8) !important;  /* 保持原始背景 */
        }

        /* 现代化按钮动画效果 */
        .btn:hover {
            transform: translateY(-0.5px);
        }

        .btn:active {
            transform: translateY(0);
        }

        /* 按钮点击波纹效果 */
        .btn {
            position: relative;
            overflow: hidden;
        }

        .btn::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            transform: translate(-50%, -50%);
            transition: width 0.3s, height 0.3s;
        }

        .btn:active::after {
            width: 200px;
            height: 200px;
        }

        /* 侧边栏优化 - 增强呼吸感 */
        #sidebar {
            backdrop-filter: blur(10px);  /* 毛玻璃效果 */
        }

        /* 代码块优化 - 零边界风格 */
        .code-block-container {
            border: 1px solid rgba(0,0,0,0.06) !important;  /* 极淡边框 */
            background: rgba(248,250,252,0.8) !important;   /* 半透明背景 */
        }

    </style>

    <!-- External JS Libraries with CDN fallback -->
    <script>
        (function() {
            // DOMPurify 多CDN回退
            const dompurifySources = [
                'https://cdnjs.cloudflare.com/ajax/libs/dompurify/3.0.11/purify.min.js',
                'https://cdn.jsdelivr.net/npm/dompurify@3.0.11/dist/purify.min.js',
                'https://unpkg.com/dompurify@3.0.11/dist/purify.min.js'
            ];

            // highlight.js 多CDN回退
            const highlightSources = [
                'https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js',
                'https://cdn.jsdelivr.net/npm/highlight.js@11.9.0/lib/index.min.js',
                'https://unpkg.com/@highlightjs/cdn-assets@11.9.0/highlight.min.js'
            ];

            // 使用统一的脚本加载器加载DOMPurify
            window.ScriptLoader.loadWithFallback(
                dompurifySources,
                () => typeof DOMPurify !== 'undefined',
                'DOMPurify',
                function() {
                    // 加载完DOMPurify后加载highlight.js
                    window.ScriptLoader.loadWithFallback(
                        highlightSources,
                        () => typeof hljs !== 'undefined',
                        'highlight'
                    );
                }
            );
        })();
    </script>
    <!-- marked.js 和 mermaid.js 已通过多CDN回退机制在页面头部加载 -->
</head>

<body class=""> <!-- Theme class added dynamically by JS -->

<div id="app-container">

    <!-- Top Navigation Bar -->
    <header id="top-bar" role="banner">
        <button id="menu-toggle-button" title="打开/关闭侧边栏" aria-label="打开/关闭侧边栏" aria-expanded="false">
            <span aria-hidden="true"></span>
            <span aria-hidden="true"></span>
            <span aria-hidden="true"></span>
        </button>
        <h1 id="chat-title">LuckyStar</h1>
        <nav id="top-actions" role="navigation" aria-label="主要操作">
            <button id="search-message-button" class="top-action-button" title="搜索消息" aria-label="搜索消息">
                <i class="fas fa-search" aria-hidden="true"></i>
            </button>
            <button id="theme-toggle-button" class="top-action-button" title="切换主题" aria-label="切换主题">
                <i class="fas fa-sun" aria-hidden="true"></i>
            </button>
            <button id="new-chat-button-top" class="top-action-button" title="新建对话" aria-label="新建对话">
                <i class="fas fa-plus-square" aria-hidden="true"></i>
            </button>
            <button id="settings-button-top" class="top-action-button" title="API 设置" aria-label="API 设置">
                <i class="fas fa-cog" aria-hidden="true"></i>
            </button>
        </nav>
    </header>

    <!-- Conversation Options Bar -->
    <div id="conversation-options-bar">
        <label for="model-selector-trigger">模型:</label>
        <div class="custom-selector-container">
            <button id="model-selector-trigger" class="custom-selector-trigger" aria-haspopup="listbox" aria-expanded="false" disabled>
                <span class="selected-value-display">加载中...</span>
                <i class="fas fa-chevron-down"></i>
            </button>
            <div id="model-selector-panel" class="custom-selector-panel" role="listbox"></div>
        </div>
        <div class="conversation-settings-button-wrapper">
            <button id="conversation-settings-button" title="对话设置"> <i class="fas fa-sliders-h"></i> </button>
        </div>
    </div>

    <!-- Scroll Buttons -->
    <div id="scroll-buttons-container" class="scroll-buttons-container">
        <button id="scroll-to-top-button" class="scroll-button" title="回到顶部">
            <i class="fas fa-chevron-up"></i>
        </button>
        <button id="scroll-to-bottom-button" class="scroll-button" title="回到底部">
            <i class="fas fa-chevron-down"></i>
        </button>
    </div>

    <!-- Sidebar Navigation -->
    <aside id="sidebar" role="complementary" aria-label="侧边栏导航">
        <header id="sidebar-header">
            <h2 style="font-weight: 500; margin: 0;">对话列表</h2>
            <button id="sidebar-close-button" title="关闭菜单" aria-label="关闭菜单">&times;</button>
        </header>
        <section id="sidebar-controls" aria-label="对话控制">
            <div class="search-input-container">
                <input type="search" id="session-search-input" placeholder="搜索对话标题..." aria-label="搜索对话标题">
            </div>
            <div class="sidebar-io-buttons" role="group" aria-label="导入导出操作">
                 <button id="import-conversation-button" class="sidebar-io-button" title="导入对话文件 (JSON)" aria-label="导入对话文件">
                     <i class="fas fa-file-import" aria-hidden="true"></i> 导入
                 </button>
                 <button id="export-conversation-button" class="sidebar-io-button" title="导出当前对话 (JSON)" aria-label="导出当前对话">
                     <i class="fas fa-file-export" aria-hidden="true"></i> 导出
                 </button>

            </div>
            <button id="view-backend-log-button" class="sidebar-io-button" title="查看后端日志" aria-label="查看后端日志" style="margin-top: 8px;">
                <i class="fas fa-file-alt" aria-hidden="true"></i> 查看后端日志
            </button>
        </section>
        <nav id="session-list" role="navigation" aria-label="对话列表"></nav> <!-- Regular sessions go here -->
        <!-- Agent List Section -->
        <section id="agent-list" aria-label="智能体列表">
            <header id="agent-list-header">
                 <h3 style="margin: 0;">智能体 (Agents)</h3>
                 <button id="create-agent-button" title="创建新智能体" aria-label="创建新智能体">
                     <i class="fas fa-plus-circle" aria-hidden="true"></i>
                 </button>
            </header>
            <nav id="agent-list-items" role="navigation" aria-label="智能体导航"></nav> <!-- Agents go here -->
        </section>
    </aside>
    <div id="sidebar-overlay"></div>

    <!-- Main Content Area -->
    <main id="main-content" role="main" aria-label="聊天主界面">
        <section id="chatbox" role="log" aria-live="polite" aria-label="聊天消息">
            <!-- Messages will be appended here by JavaScript -->
            <!-- Empty state might be injected here by JavaScript -->
        </section>
        <aside id="message-search-container" role="search" aria-label="消息搜索">
            <input type="search" id="message-search-input" placeholder="搜索消息内容..." aria-label="搜索消息内容">
            <button id="message-search-prev" title="上一个匹配" aria-label="上一个匹配">&lt;</button>
            <button id="message-search-next" title="下一个匹配" aria-label="下一个匹配">&gt;</button>
            <button id="message-search-close" title="关闭搜索" aria-label="关闭搜索">&times;</button>
        </aside>
    </main>

    <!-- Input Area (V48 Redesign) -->
    <section id="input-container" role="region" aria-label="消息输入区域">

         <!-- 移除旧的控制区域，功能按钮将移动到输入框内部 -->
         <!-- 重新设计的输入区域 -->
        <div id="input-textarea-wrapper" role="group" aria-label="消息输入">
            <textarea id="message-input" placeholder="向 openai 发送消息... (Ctrl/Shift+Enter 换行)" rows="1"
                      aria-label="消息输入框" aria-describedby="send-button"
                      aria-multiline="true" role="textbox"></textarea>

            <!-- 底部工具栏 -->
            <div id="input-toolbar" role="toolbar" aria-label="输入工具栏">
                <!-- 左侧功能按钮 -->
                <div id="toolbar-left">
                    <!-- 文件上传按钮 -->
                    <button id="file-upload-button" class="toolbar-button" title="上传文件" aria-label="上传文件">
                        <i class="fas fa-paperclip" aria-hidden="true"></i>
                    </button>
                    <input type="file" id="file-upload-input" style="display: none;" multiple
                           accept="image/*,audio/*,video/*,.pdf,.doc,.docx,.txt,.json,.csv,.xlsx,.xls">

                    <!-- 语音功能按钮 -->
                    <button id="voice-function-button" class="toolbar-button" title="语音功能" aria-label="语音功能">
                        <i class="fas fa-microphone" aria-hidden="true"></i>
                    </button>

                    <!-- AI工具集按钮 -->
                    <button id="ai-tools-button" class="toolbar-button" title="AI工具集" aria-label="AI工具集">
                        <i class="fas fa-tools" aria-hidden="true"></i>
                    </button>

                    <!-- 图像生成按钮 -->
                    <button id="image-gen-button" class="toolbar-button" title="AI 图像生成" aria-label="AI 图像生成">
                        <i class="fas fa-palette" aria-hidden="true"></i>
                    </button>

                    <!-- 智能分析开关按钮 -->
                    <button id="tts-toggle-button" class="toolbar-toggle-button"
                            data-enabled="false" title="智能分析" aria-label="智能分析开关">
                        <i class="fas fa-lightbulb" aria-hidden="true"></i>
                    </button>

                    <!-- 网络搜索开关按钮 -->
                    <button id="web-search-toggle-button" class="toolbar-toggle-button"
                            data-enabled="false" title="网络搜索" aria-label="网络搜索开关">
                        <i class="fas fa-globe" aria-hidden="true"></i>
                    </button>
                </div>

                <!-- 右侧发送按钮 -->
                <div id="toolbar-right">
                    <button id="send-button" title="发送消息" aria-label="发送消息" disabled
                            aria-describedby="message-input">
                        <i class="fas fa-arrow-up" aria-hidden="true"></i>
                    </button>
                </div>
            </div>
        </div>
    </section>

</div> <!-- app-container end -->

    <!-- Empty State Template -->
    <template id="empty-state-template">
        <div class="empty-state-content">
            <div class="logo-container">
                 <img src="/static/logo.svg" alt="Logo" class="logo">
            </div>
            <div class="welcome-title">欢迎使用 LuckyStar</div>
            <div class="model-name-display">当前模型: <span>AI 模型</span></div>
            <p class="welcome-message">有什么我可以帮你的吗？</p>
            <div class="suggestion-buttons">
                <button class="suggestion-button" data-prompt="写一首关于夏夜的诗" title="发送提示: 写一首关于夏夜的诗">写一首关于夏夜的诗</button>
                <button class="suggestion-button" data-prompt="用简单的语言解释黑洞是什么" title="发送提示: 用简单的语言解释黑洞是什么">解释黑洞是什么</button>
                <button class="suggestion-button" data-prompt="我应该如何学习 Python？" title="发送提示: 我应该如何学习 Python？">如何学习 Python？</button>
            </div>
        </div>
    </template>

    <!-- API Settings Modal -->
    <div id="settings-modal" class="modal">
        <div class="modal-content">
            <span id="modal-close-button-api" class="modal-close-button" title="关闭 API 设置">&times;</span>
            <h2>API 配置管理</h2>
            <div class="modal-profile-selector">
                <label for="settings-profile-selector-trigger">配置:</label>
                <div id="settings-profile-selector-container" class="custom-selector-container">
                    <button id="settings-profile-selector-trigger" class="custom-selector-trigger" aria-haspopup="listbox" aria-expanded="false">
                        <span class="selected-value-display">新建配置 (或选择模板)</span>
                        <i class="fas fa-chevron-down"></i>
                    </button>
                    <div id="settings-profile-selector-panel" class="custom-selector-panel" role="listbox"></div>
                </div>
            </div>
            <div class="modal-form-area">
                <div class="form-group" data-validate="profile-name">
                    <label for="profile-name-input">配置名称:</label>
                    <input type="text" id="profile-name-input" placeholder="例如：我的 OpenRouter Key" required>
                    <div class="invalid-feedback">请输入配置名称。</div>
                </div>
                <div class="form-group" data-validate="api-key">
                    <label for="api-key-input">API Key:</label> <!-- JS will add required/optional text -->
                    <div class="input-with-button">
                        <input type="password" id="api-key-input" placeholder="sk-or-v1-xxxxxxxx">
                        <button id="toggle-api-key-visibility" class="toggle-visibility-button" type="button" title="切换可见性"> <i class="fas fa-eye"></i> </button>
                    </div>
                    <div class="invalid-feedback">请输入 API Key。</div>
                </div>
                <div class="form-group" data-validate="api-endpoint">
                    <label for="api-endpoint-input">API Endpoint:</label>
                    <input type="text" id="api-endpoint-input" placeholder="https://provider.com/v1/chat/completions" required>
                    <div class="invalid-feedback">请输入 API Endpoint。</div>
                </div>
                <div class="model-selection-group-container form-group">
                    <label for="settings-model-selector-trigger">可用模型 (首选模型):</label>
                    <div class="model-selection-group">
                        <div class="custom-selector-container">
                            <button id="settings-model-selector-trigger" class="custom-selector-trigger" aria-haspopup="listbox" aria-expanded="false">
                                <span class="selected-value-display">选择模型</span>
                                <i class="fas fa-chevron-down"></i>
                            </button>
                            <div id="settings-model-selector-panel" class="custom-selector-panel" role="listbox"></div>
                        </div>
                    </div>
                    <div class="invalid-feedback"></div>
                </div>
                <div class="form-group">
                     <label for="max-tokens-input">Max Tokens (可选):</label>
                     <input type="number" id="max-tokens-input" placeholder="留空则无限制" min="1">
                     <div class="invalid-feedback">请输入有效的数字（可选）。</div>
                </div>
            </div>
            <div class="modal-actions">
                <div class="modal-action-row top-row">
                    <button id="set-default-button" title="将当前选中配置设为默认" disabled> <i class="fas fa-star"></i> 设为默认 </button>
                    <button id="save-as-template-button" title="将当前设置另存为模板" disabled> <i class="fas fa-bookmark"></i> 存为模板 </button>
                    <button id="manage-templates-button" title="管理我的模板"> <i class="fas fa-list-alt"></i> 管理模板 </button>
                </div>
                <div class="modal-action-row bottom-row">
                    <button id="delete-profile-button" title="删除当前选中的配置" disabled> <i class="fas fa-trash-alt"></i> 删除 </button>
                    <button id="cancel-settings-button" title="取消并关闭窗口"> <i class="fas fa-times"></i> 取消 </button>
                    <button id="save-changes-button" title="保存修改或新建配置" disabled> <i class="fas fa-floppy-disk"></i> 保存 </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Conversation Settings Modal -->
    <div id="conversation-settings-modal" class="modal">
        <div class="modal-content">
            <span id="modal-close-button-conversation" class="modal-close-button" title="关闭对话设置">&times;</span>
            <h2>对话设置</h2>
            <div class="modal-form-area">
                <div class="form-group">
                    <label for="conversation-title-input">对话标题:</label>
                    <input type="text" id="conversation-title-input" placeholder="输入对话标题" required>
                    <div class="invalid-feedback">请输入对话标题。</div>
                </div>
                <div class="form-group">
                    <label for="conversation-system-prompt-input">系统提示 (System Prompt):</label>
                    <textarea id="conversation-system-prompt-input" rows="5" placeholder="设置 AI 的角色或行为指令，例如：你是一个乐于助人的助手。"></textarea>
                </div>
                <div class="form-group tts-voice-selector-container">
                     <label for="conversation-tts-voice-selector-trigger">朗读音色:</label>
                     <div class="custom-selector-container">
                          <button id="conversation-tts-voice-selector-trigger" class="custom-selector-trigger" aria-haspopup="listbox" aria-expanded="false">
                              <span class="selected-value-display">选择音色</span>
                              <i class="fas fa-chevron-down"></i>
                          </button>
                          <div id="conversation-tts-voice-selector-panel" class="custom-selector-panel" role="listbox"></div>
                      </div>
                </div>

            </div>
            <div class="modal-actions">
                 <div class="modal-action-row bottom-row">
                     <button id="cancel-conversation-settings-button" title="取消修改"> <i class="fas fa-times"></i> 取消 </button>
                     <button id="save-conversation-settings-button" title="保存对话设置"> <i class="fas fa-floppy-disk"></i> 保存 </button>
                 </div>
            </div>
        </div>
    </div>

    <!-- Manage Templates Modal -->
    <div id="manage-templates-modal" class="modal">
        <div class="modal-content">
            <span class="modal-close-button" onclick="closeManageTemplatesModal()" title="关闭窗口">&times;</span>
            <h2>管理我的模板</h2>
            <ul id="user-template-list"></ul>
            <div class="modal-actions" style="text-align: right; margin-top: 15px;">
                <button onclick="closeManageTemplatesModal()" class="custom-prompt-button" style="background-color: #6c757d;" title="关闭模板管理">关闭</button>
            </div>
        </div>
    </div>

    <!-- Custom Confirm -->
    <div id="custom-confirm-overlay">
        <div id="custom-confirm-box">
            <div id="custom-confirm-message">确定要执行此操作吗？</div>
            <div id="custom-confirm-buttons">
                <button id="custom-confirm-no" class="custom-confirm-button" title="取消操作">取消</button>
                <button id="custom-confirm-yes" class="custom-confirm-button" title="确认操作">确定</button>
            </div>
        </div>
    </div>

    <!-- Custom Prompt -->
    <div id="custom-prompt-overlay">
        <div id="custom-prompt-box">
            <div id="custom-prompt-message">请输入：</div>
            <div id="custom-prompt-input-container">
                 <input type="text" id="custom-prompt-input-text">
            </div>
            <div id="custom-prompt-buttons">
                <button id="custom-prompt-cancel" class="custom-prompt-button" title="取消输入">取消</button>
                <button id="custom-prompt-ok" class="custom-prompt-button" title="确认输入">确定</button>
            </div>
        </div>
    </div>

    <!-- HTML Runner Modal -->
    <div id="html-runner-modal" class="modal">
        <div id="html-runner-content">
            <div id="html-runner-header">
                <span id="html-runner-title">运行 HTML</span>
                <button id="html-runner-close-button" title="关闭预览">&times;</button>
            </div>
            <div id="html-runner-iframe-container">
                <iframe id="html-runner-iframe" sandbox="allow-scripts allow-popups allow-forms" referrerpolicy="no-referrer"></iframe>
            </div>
        </div>
    </div>

    <!-- Code Viewer Modal -->
    <div id="code-viewer-modal" class="modal">
        <div id="code-viewer-content">
            <div id="code-viewer-header">
                <span id="code-viewer-title">代码预览</span>
                <div id="code-viewer-header-buttons">
                     <button id="code-viewer-copy-button" title="复制代码"><i class="far fa-copy"></i> 复制</button>
                     <button id="code-viewer-download-button" title="下载代码"><i class="fas fa-save"></i> 保存</button>
                     <button id="code-viewer-close-button" title="关闭预览">&times;</button>
                </div>
            </div>
            <div id="code-viewer-code-container">
                 <pre>
                     <div class="code-scroll-wrapper">
                         <code class="hljs"></code>
                     </div>
                 </pre>
            </div>
        </div>
    </div>

    <!-- 全新设计的AI图像生成界面 -->
    <div id="image-gen-modal" class="modal">
        <div class="modal-content">
            <span class="modal-close-button" onclick="closeImageGenModal()" title="关闭图像生成">&times;</span>
            <h2>AI 图像生成</h2>
            <div class="modal-form-area">
                <!-- 主要描述区域 -->
                <div class="image-gen-form-group">
                    <label for="image-gen-prompt-input">图像描述</label>
                    <textarea id="image-gen-prompt-input" class="image-gen-textarea" placeholder="例如：一只宇航员猫在月球上骑自行车"></textarea>
                    <div id="image-gen-prompt-actions">
                         <button id="enhance-prompt-button" title="使用 AI 优化提示词"><i class="fas fa-magic"></i> 优化</button>
                         <button id="undo-enhance-prompt-button" title="撤销优化" disabled><i class="fas fa-undo"></i> 撤销</button>
                         <button id="clear-prompt-button" title="清空提示词"><i class="fas fa-times-circle"></i> 清空</button>
                    </div>

                    <!-- 提示词优化底部栏 -->
                    <div id="prompt-optimization-bar" class="prompt-optimization-bar">
                        <div class="optimization-input-container">
                            <input type="text" id="prompt-optimization-input" class="prompt-optimization-input" placeholder="描述你想要的图像细节或风格，AI将优化你的提示词...">
                            <button id="prompt-optimization-send" class="prompt-optimization-send" title="优化提示词">
                                <i class="fas fa-magic"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="image-gen-form-group" style="margin-top: 20px;">
                    <label for="image-gen-negative-prompt-input">负面提示词</label>
                    <textarea id="image-gen-negative-prompt-input" class="image-gen-textarea" style="min-height: 60px; max-height: 120px; color: #999;" placeholder="worst quality, blurry"></textarea>
                </div>
                <div id="image-gen-options">
                    <!-- 基础参数卡片 -->
                    <div class="image-gen-card">
                        <div class="image-gen-card-title">基础参数</div>
                        <div class="image-gen-grid basic-params">
                            <div class="image-gen-form-group">
                                <label for="image-gen-model-selector-trigger">模型</label>
                                <div class="image-gen-selector">
                                    <button id="image-gen-model-selector-trigger" class="custom-selector-trigger" aria-haspopup="listbox" aria-expanded="false">
                                        <span class="selected-value-display">选择模型</span> <i class="fas fa-chevron-down"></i>
                                    </button>
                                    <div id="image-gen-model-selector-panel" class="custom-selector-panel" role="listbox"></div>
                                </div>
                            </div>
                            <div class="image-gen-form-group">
                                <label for="image-gen-aspect-ratio-selector-trigger">宽高比</label>
                                <div class="image-gen-selector">
                                    <button id="image-gen-aspect-ratio-selector-trigger" class="custom-selector-trigger" aria-haspopup="listbox" aria-expanded="false">
                                        <span class="selected-value-display">1:1 (默认)</span> <i class="fas fa-chevron-down"></i>
                                    </button>
                                    <div id="image-gen-aspect-ratio-selector-panel" class="custom-selector-panel" role="listbox"></div>
                                </div>
                            </div>
                            <div class="image-gen-form-group">
                                <label for="image-gen-batch-count-input">生成数量</label>
                                <input type="number" id="image-gen-batch-count-input" class="image-gen-input" min="1" max="16" value="4" placeholder="4">
                            </div>
                        </div>
                    </div>

                    <!-- 尺寸参数卡片 -->
                    <div class="image-gen-card">
                        <div class="image-gen-card-title">尺寸设置</div>
                        <div class="image-gen-grid size-params">
                            <div class="image-gen-form-group">
                                <label for="image-gen-width-input">宽度</label>
                                <input type="number" id="image-gen-width-input" class="image-gen-input" min="64" max="2048" step="8" placeholder="1024">
                            </div>
                            <div class="image-gen-form-group">
                                <label for="image-gen-height-input">高度</label>
                                <input type="number" id="image-gen-height-input" class="image-gen-input" min="64" max="2048" step="8" placeholder="1024">
                            </div>
                            <div class="image-gen-form-group" style="grid-column: span 2;">
                                <label for="image-gen-seed-input">种子值</label>
                                <div style="display: flex; align-items: center; gap: 8px;">
                                    <input type="number" id="image-gen-seed-input" class="image-gen-input" placeholder="留空随机" min="0">
                                    <button id="image-gen-random-seed-button" class="image-gen-action-button secondary" title="随机种子"><i class="fas fa-random"></i></button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- 生成选项卡片 -->
                    <div class="image-gen-card">
                        <div class="image-gen-card-title">生成选项</div>
                        <div class="image-gen-grid toggle-params">
                            <div class="image-gen-toggle-group">
                                <input type="checkbox" id="image-gen-nologo-toggle" checked>
                                <label for="image-gen-nologo-toggle">去除 Logo</label>
                            </div>
                            <div class="image-gen-toggle-group">
                                <input type="checkbox" id="image-gen-enhance-toggle">
                                <label for="image-gen-enhance-toggle">增强 Prompt</label>
                            </div>
                            <div class="image-gen-toggle-group">
                                <input type="checkbox" id="image-gen-safe-toggle" checked>
                                <label for="image-gen-safe-toggle">安全模式</label>
                            </div>
                        </div>
                    </div>

                    <!-- 高级选项卡片 -->
                    <div class="image-gen-card">
                        <div class="image-gen-card-title">高级选项</div>
                        <div class="image-gen-grid advanced-params">
                            <div class="image-gen-form-group">
                                <label for="image-gen-referrer-input">Referrer</label>
                                <input type="text" id="image-gen-referrer-input" class="image-gen-input" placeholder="https://pollinations.ai" style="color: #999;">
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Result Area -->
                <div id="image-gen-result-container" class="hidden">
                    <div class="result-header">
                        <i class="fas fa-check-circle"></i>
                        <span>生成完成</span>
                    </div>
                    <div class="image-display-frame">
                       <img id="image-gen-result-image" src="#" alt="生成的图像">
                    </div>
                </div>

                <!-- Preview Frame - 根据比例预设大小的画框 -->
                <div id="image-gen-preview-container" class="hidden">
                    <div class="preview-header">
                        <i class="fas fa-magic"></i>
                        <span>正在生成图像</span>
                    </div>
                    <div id="image-gen-preview-frame" class="image-preview-frame loading">
                        <div class="preview-loading">
                            <i class="fas fa-spinner fa-spin"></i>
                            <span>AI 正在创作中...</span>
                        </div>
                    </div>
                </div>

                <!-- 批量生成预览区域 -->
                <div id="image-gen-batch-preview-container" class="hidden">
                    <div class="batch-progress-header">
                        <span class="batch-progress-text">生成进度: <span id="batch-progress-text">0/4</span></span>
                        <div class="batch-progress-bar">
                            <div class="batch-progress-fill" id="batch-progress-fill"></div>
                        </div>
                    </div>
                    <div id="image-gen-batch-grid" class="image-batch-grid" data-count="4">
                        <!-- 批量图片将在这里动态生成 -->
                    </div>
                </div>

                 <div id="image-gen-progress" class="hidden">
                     <i class="fas fa-spinner fa-spin"></i> 正在生成图像...
                 </div>
            </div>

            <!-- 现代化按钮区域 -->
            <div class="modal-actions" style="display: flex; gap: 12px; justify-content: flex-end; padding: 20px 24px; border-top: 1px solid #e2e8f0; background: #f8fafc;">
                 <button id="image-gen-submit-button" class="image-gen-action-button primary" title="生成单张图像">
                     <i class="fas fa-wand-magic-sparkles"></i> 单张生成
                 </button>
                 <button id="image-gen-batch-button" class="image-gen-action-button primary" title="批量生成图像">
                     <i class="fas fa-layer-group"></i> 批量生成
                 </button>
                 <button id="image-gen-close-button" class="image-gen-action-button secondary" onclick="closeImageGenModal()" title="关闭">
                     <i class="fas fa-xmark"></i> 关闭
                 </button>
            </div>
        </div>
    </div>



    <!-- 增强的图片查看器 -->
    <div id="image-viewer-modal" class="modal" onclick="closeImageViewerModal()">
         <div id="image-viewer-content" onclick="event.stopPropagation()">
             <img id="image-viewer-img" src="#" alt="放大图像">

             <!-- 图片元数据悬停显示面板 -->
             <div id="image-metadata-overlay" class="image-metadata-overlay">
                 <div class="metadata-content">
                     <div class="metadata-header">
                         <i class="fas fa-info-circle metadata-header-icon"></i>
                         <span class="metadata-header-title">图片信息</span>
                     </div>
                     <div class="metadata-row">
                         <i class="fas fa-comment-dots metadata-icon"></i>
                         <span class="metadata-label">提示词</span>
                         <span class="metadata-value" id="metadata-prompt">-</span>
                     </div>
                     <div class="metadata-row">
                         <i class="fas fa-brain metadata-icon"></i>
                         <span class="metadata-label">模型</span>
                         <span class="metadata-value highlight" id="metadata-model">-</span>
                     </div>
                     <div class="metadata-row">
                         <i class="fas fa-expand-arrows-alt metadata-icon"></i>
                         <span class="metadata-label">分辨率</span>
                         <span class="metadata-value" id="metadata-resolution">-</span>
                     </div>
                     <div class="metadata-row">
                         <i class="fas fa-dice metadata-icon"></i>
                         <span class="metadata-label">种子值</span>
                         <span class="metadata-value highlight" id="metadata-seed">-</span>
                     </div>
                     <div class="metadata-row" id="metadata-negative-row" style="display: none;">
                         <i class="fas fa-ban metadata-icon"></i>
                         <span class="metadata-label">负面提示</span>
                         <span class="metadata-value" id="metadata-negative">-</span>
                     </div>
                     <div class="metadata-row" id="metadata-options-row" style="display: none;">
                         <i class="fas fa-cogs metadata-icon"></i>
                         <span class="metadata-label">选项</span>
                         <span class="metadata-value" id="metadata-options">-</span>
                     </div>
                 </div>
             </div>

             <!-- 左右翻页按钮 -->
             <button id="image-viewer-prev-button" class="image-viewer-nav-button prev" title="上一张" onclick="navigateImage(-1)">
                 <i class="fas fa-chevron-left"></i>
             </button>
             <button id="image-viewer-next-button" class="image-viewer-nav-button next" title="下一张" onclick="navigateImage(1)">
                 <i class="fas fa-chevron-right"></i>
             </button>

             <!-- 图片信息显示 -->
             <div id="image-viewer-info" class="image-viewer-info">
                 <span id="image-viewer-counter">1 / 1</span>
             </div>

             <!-- 操作按钮 -->
             <div id="image-viewer-actions" class="image-viewer-actions">
                 <button id="image-viewer-save-button" class="image-viewer-action-button save" title="保存图片">
                     <i class="fas fa-floppy-disk"></i>
                     <span>保存</span>
                 </button>
                 <button id="image-viewer-close-button" class="image-viewer-action-button close" title="关闭" onclick="closeImageViewerModal()">
                     <i class="fas fa-xmark"></i>
                     <span>关闭</span>
                 </button>
             </div>
         </div>
    </div>

    <!-- Toast Notification Container -->
    <div id="toast-container"></div>

<script src="static/js/main.js" defer></script>



    <!-- Backend Log Modal (Modern Design) -->
    <div id="backend-log-modal" class="modal">
        <div class="backend-log-modal-content">
            <div class="backend-log-modal-header">
                <h2><i class="fas fa-terminal"></i> 后端日志控制台</h2>
                <div class="backend-log-modal-actions">
                    <button id="refresh-log-button" title="刷新日志"><i class="fas fa-sync-alt"></i> 刷新</button>
                    <button id="clear-log-display-button" title="清空日志显示"><i class="fas fa-eraser"></i> 清空显示</button>
                    <button id="close-backend-log-modal" title="关闭"><i class="fas fa-times"></i> 关闭</button>
                </div>
            </div>
            <div id="backend-log-display">
                <p><i class="fas fa-spinner fa-spin"></i> <span class="loading-text">正在加载日志内容...</span></p>
            </div>
        </div>
    </div>

    <!-- 思考过程模板 -->
    <template id="thinking-process-template">
        <div class="thinking-process-container" data-completed="false" data-think-index="">
            <div class="thinking-process-header">
                <div class="thinking-process-title">
                    <i class="fas fa-lightbulb"></i>
                    <span class="thinking-status">思考中</span>
                </div>
                <span class="thinking-time">
                    <span class="thinking-time-counter">0</span>秒
                </span>
                <button class="thinking-process-toggle"><i class="fas fa-chevron-down"></i></button>
            </div>
            <div class="thinking-process-content"></div>
        </div>
    </template>

    <!-- 语音功能面板 -->
    <div id="voice-function-panel" class="function-panel" style="display: none;">
        <div class="function-panel-content">
            <div class="function-panel-header">
                <h3><i class="fas fa-microphone"></i> 语音功能</h3>
                <button id="voice-panel-close" class="panel-close-button" title="关闭">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="function-panel-body">
                <!-- STT 语音转文本 -->
                <div class="voice-function-section">
                    <h4><i class="fas fa-microphone-alt"></i> 语音转文本 (STT)</h4>
                    <div class="voice-controls">
                        <button id="start-recording-button" class="btn btn-primary">
                            <i class="fas fa-microphone"></i> 开始录音
                        </button>
                        <button id="stop-recording-button" class="btn btn-secondary" disabled>
                            <i class="fas fa-stop"></i> 停止录音
                        </button>
                        <div id="recording-status" class="recording-status"></div>
                    </div>
                    <div id="stt-result" class="stt-result"></div>
                </div>

                <!-- TTS 文本转语音 -->
                <div class="voice-function-section">
                    <h4><i class="fas fa-volume-up"></i> 文本转语音 (TTS)</h4>
                    <div class="voice-controls">
                        <div class="voice-selector-container">
                            <label for="voice-selector">选择音色:</label>
                            <select id="voice-selector" class="form-select">
                                <option value="">默认音色</option>
                            </select>
                        </div>
                        <div class="tts-controls">
                            <button id="test-voice-button" class="btn btn-outline">
                                <i class="fas fa-play"></i> 试听
                            </button>
                            <button id="read-last-message-button" class="btn btn-outline">
                                <i class="fas fa-comment"></i> 朗读最后消息
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- AI工具集管理面板 -->
    <div id="ai-tools-panel" class="function-panel" style="display: none;">
        <div class="function-panel-content">
            <div class="function-panel-header">
                <h3><i class="fas fa-tools"></i> AI工具集管理</h3>
                <button id="ai-tools-panel-close" class="panel-close-button" title="关闭">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="function-panel-body">
                <div class="tools-description">
                    <p>管理AI可用的Function Calling工具，您可以选择性地启用或禁用特定工具。</p>
                </div>
                <div id="ai-tools-list" class="ai-tools-list">
                    <!-- 工具列表将通过JavaScript动态生成 -->
                </div>
                <div class="tools-actions">
                    <button id="enable-all-tools" class="btn btn-primary">
                        <i class="fas fa-check-double"></i> 全部启用
                    </button>
                    <button id="disable-all-tools" class="btn btn-secondary">
                        <i class="fas fa-times-circle"></i> 全部禁用
                    </button>
                    <button id="reset-tools-default" class="btn btn-outline">
                        <i class="fas fa-undo"></i> 恢复默认
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 文件上传状态显示 -->
    <div id="file-upload-status" class="file-upload-status" style="display: none;">
        <div class="file-status-content">
            <div class="file-status-header">
                <span class="file-status-title">文件上传状态</span>
                <button id="file-status-close" class="file-status-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div id="file-status-list" class="file-status-list">
                <!-- 文件状态项将通过JavaScript动态添加 -->
            </div>
        </div>
    </div>

</body>
</html>