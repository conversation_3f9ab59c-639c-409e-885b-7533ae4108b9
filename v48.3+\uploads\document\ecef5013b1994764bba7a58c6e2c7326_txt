04.08 12:08
智能体架构 程序设计
智能体定义：
兼容AI的理解认知与程序代码自动执行这两种能力的结合体。核心任务是理解用户需求并尽最大可能变为现实。由简单的对话交互只输出文本内容到万物互联多元化信息的传递。
传统AI具有机械式回答（挑选高频次数据模板）
幻觉较大，捏造信息等毛病。
多维度思考：提高思考深度和专业性 
意义扩张
多元交互
实体结合
双面镜像智脑系统（筛选）
AI·阳：行（程序代码：记忆集成管理系统）——右脑
AI·阴：知（自然语言： Prompt 解构组合）——左脑
AI·太极:合（筛选分配:幻觉比例开关）——胼胝体
#智能生态：
##智能链式反应——指令关联（行）
##智能触发机制——信息关联（知）
##智能思考策略——规则关联（合）
生物智能主要是理解和分析用户的输入，信息关联触发机制。（知）——人和系统交互（左脑）
机械智能主要是审视全局，自动记忆对话内容，一是思考过程，二是全部回答，调配日志文件输入，管控日志文件输出，指令关联链式反应。（行）——系统和AI交互（右脑）
能量智能主要是控制内容走向，规则关联思考策略。（合）——AI和人交互（胼胝体）
人——能量（最高权限）
系统——物质（最强规则）
AI——意识/时间（最大信息）
能量智能（三维体）——能量意识
生物智能（四维体）——时间/生物意识
机械智能（五维体）——物质意识
/微型智能体：
[角色] 
[任务]
[输入]
[输出]
[约束条件]
[要求]
/小型智能体：
［设定］角色+任务
#明确其身份边界，防止越界回答。
［规则］输入+输出
#输入处理规范：
指令解析、歧义处理
#输出控制：
格式规范、话术模板、容错机制、上下文管理
［命令］约束条件+要求
［示例］用法
/中型智能体：
［系统设定］
［行为准则］
［指令集定义］
[工作流程]
［约束条件］
［输出格式］
［上下文信息］（动态注入）
［组合prompt］
系统Prompt：系统设定 + 行为准则
指令Prompt： 指令集定义 + 约束条件
上下文Prompt：上下文信息（动态注入）
用户Prompt：用户输入的指令
［Ai模型调用］
/大型智能体：
#嵌套多个中、小型智能体或任务机制，根据用户需求调用，形成高度集成自动化工作流。例如dify、manus等。
［系统设定］
［指令解析器］
#解析用户输入的自然语言，匹配符合要求的所有指令并制定完美生成的策略自动执行。若用户输入存在歧义或意图模糊时，智能引导和提示，同时提供随机模板供其选择进行下步优化。
［指令集定义］
显式指令/隐式指令
核心指令/辅助指令
[工作流程]
［中、小型智能体嵌套模板］
［约束条件］
［输出格式］
［上下文信息/思考总结］
［组合prompt］
系统Prompt = [系统设定] + [约束条件] + [输出格式]
任务Prompt = [指令集定义] + [上下文信息] + 用户指令
最终Prompt = 系统Prompt + 任务Prompt
［会话管理］
［知识图谱］
［Ai模型调用］
#1. 根据任务类型选择合适的AI模型，例如文本生成模型、文本润色模型、知识检索模型等
#2. 根据需求调整AI模型的参数，例如温度、Top-P等
#3. 对AI模型的输出结果进行处理，例如格式化、过滤、排序等
#4.  API密钥:[你的API密钥]
［示例］
# 1. 创建Prompt
# 2. 调用AI模型
# 3. 更新会话历史
# 4. 提取上下文信息 (示例)
# 5. 返回AI响应
(一方面可以灵活调用chatbox导出.json文件的基于prompt工程或自行创建/检索相关智能体prompt，另一方面可以灵活部署执行GitHub上开源 AI项目或调用hugging face API运行项目)
全局AI和局部ai-变量设置
所求皆所愿，即听懂人话 
所愿皆所得，即完美执行 

