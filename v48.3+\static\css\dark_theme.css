/* ===================================================================
   暗黑主题样式系统 - 现代极简版本 v3.0
   基于统一设计系统，仅覆盖必要的颜色变量
   ================================================================= */

/* === 暗黑主题基础样式 === */
body.dark-theme {
    /* 基础样式重置 */
    background-color: var(--bg-primary);
    color: var(--text-primary);

    /* 简化背景效果 */
    background-image:
        radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.02) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(96, 165, 250, 0.01) 0%, transparent 50%);

    /* 基础定位 */
    position: relative;

    /* 注意：基础设计变量（间距、圆角、字体、动画等）已在design-system.css中统一定义 */
    /* 暗黑主题只覆盖必要的颜色变量和特定样式，其他变量继承自设计系统 */

    /* 暗黑主题特殊色彩 */
    --accent-blue: #60a5fa;
    --accent-purple: #a78bfa;
    --accent-green: #34d399;
    --accent-orange: #fb923c;
    --accent-red: #f87171;
    --accent-yellow: #fbbf24;

    /* === 暗黑主题特有的光影效果 === */
    /* 深度阴影层级 - 暗黑主题专用 */
    --shadow-depth-1: 0 2px 8px rgba(0, 0, 0, 0.15), 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-depth-2: 0 4px 16px rgba(0, 0, 0, 0.2), 0 2px 6px rgba(0, 0, 0, 0.15);
    --shadow-depth-3: 0 8px 32px rgba(0, 0, 0, 0.25), 0 4px 12px rgba(0, 0, 0, 0.2);
    --shadow-depth-4: 0 16px 64px rgba(0, 0, 0, 0.3), 0 8px 24px rgba(0, 0, 0, 0.25);

    /* 发光效果 - 暗黑主题专用 */
    --glow-primary: 0 0 20px rgba(59, 130, 246, 0.3);
    --glow-secondary: 0 0 16px rgba(96, 165, 250, 0.2);
    --glow-accent: 0 0 24px rgba(129, 199, 132, 0.25);

    /* 内阴影效果 - 暗黑主题专用 */
    --inset-light: inset 0 1px 0 rgba(255, 255, 255, 0.1);
    --inset-dark: inset 0 -1px 0 rgba(0, 0, 0, 0.15);
    --inset-glow: inset 0 0 16px rgba(59, 130, 246, 0.08);

    /* === 暗黑主题特有效果 === */
    /* 暗黑主题聚焦色彩覆盖 - 柔和青绿色系 */
    --focus-border-color: #81c784;
    --focus-glow-color: rgba(129, 199, 132, 0.35);

    /* 玻璃质感参数 - 暗黑主题专用 */
    --glass-blur: blur(16px) saturate(1.3) brightness(1.05);
    --glass-blur-strong: blur(20px) saturate(1.4) brightness(1.1);
    --glass-blur-subtle: blur(12px) saturate(1.2) brightness(1.02);

}

/* === 基础样式重置与全局光影增强（已合并到设计令牌系统中） === */
/* 基础样式已在第6行的设计令牌系统中统一定义，避免重复 */

/* 添加全局微妙的光影动画 */
body.dark-theme::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at var(--mouse-x, 50%) var(--mouse-y, 50%),
            rgba(59, 130, 246, 0.02) 0%,
            transparent 50%);
    pointer-events: none;
    z-index: 0;
    opacity: 0.5;
    transition: opacity 2s ease;
}

/* 全局光影增强关键帧动画 */
@keyframes ambientGlow {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

/* 微妙的脉动效果 */
@keyframes subtlePulse {
    0%, 100% {
        opacity: 0.8;
    }
    50% {
        opacity: 1;
    }
}

/* ===================================================================
   核心界面组件样式
   ================================================================= */

/* === 1. 汉堡菜单按钮样式已迁移到 design-system.css 统一管理 === */
/* 所有汉堡菜单按钮相关样式现在通过CSS变量系统在design-system.css中统一管理 */

/* === 2. 顶部栏系统 - 优化版 === */
body.dark-theme #top-bar {
    /* 简化背景渐变 */
    background: linear-gradient(135deg,
        var(--bg-secondary) 0%,
        var(--bg-tertiary) 50%,
        var(--bg-secondary) 100%);

    /* 统一边框样式 */
    border-bottom: 1px solid var(--border-primary);

    /* 玻璃质感 */
    backdrop-filter: var(--glass-blur);

    /* 阴影效果 */
    box-shadow:
        var(--inset-light),
        var(--inset-dark),
        var(--shadow-depth-2);

    /* 相对定位用于伪元素 */
    position: relative;
}

/* 添加顶部栏底部柔和过渡 */
body.dark-theme #top-bar::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    right: 0;
    height: 8px;
    background: linear-gradient(to bottom,
        rgba(30, 41, 59, 0.6) 0%,
        rgba(30, 41, 59, 0.3) 50%,
        transparent 100%);
    pointer-events: none;
    z-index: -1;
}

body.dark-theme #chat-title {
    color: var(--text-primary);
    font-weight: var(--font-weight-medium);
}

/* 顶部操作按钮 - 暗黑主题适配（简化版，继承明亮主题统一定义） */
body.dark-theme .top-action-button {
    color: var(--text-secondary);
    background: rgba(51, 65, 85, 0.6);
    border-color: rgba(71, 85, 105, 0.4);
    /* 其他样式（尺寸、圆角、内边距、过渡等）继承明亮主题的统一定义 */
}

body.dark-theme .top-action-button:hover {
    background: rgba(51, 65, 85, 0.8);
    border-color: rgba(96, 165, 250, 0.5);
    color: var(--text-primary);
}

body.dark-theme .top-action-button:active {
    background-color: rgba(71, 85, 105, 0.8);
}

/* === 3. 侧边栏系统 - 优化版 === */
body.dark-theme #sidebar {
    background: linear-gradient(135deg,
        var(--bg-secondary) 0%,
        var(--bg-primary) 50%,
        var(--bg-tertiary) 100%);
    border-right: 1px solid var(--border-primary);
    backdrop-filter: var(--glass-blur);
    box-shadow: var(--shadow-xl);
    color: var(--text-primary);
}

/* 侧边栏头部 - 只定义暗黑主题的颜色差异 */
body.dark-theme #sidebar-header {
    background: linear-gradient(135deg,
        var(--bg-tertiary) 0%,
        var(--bg-secondary) 100%);
    border-bottom: 1px solid var(--border-primary);
    color: var(--text-primary);
    /* 内边距继承明亮主题的统一定义：var(--spacing-lg) var(--spacing-lg) var(--spacing-md) var(--spacing-lg) */
}

body.dark-theme #sidebar-header h2 {
    color: var(--text-primary);
    /* 字体权重、边距等样式继承明亮主题的统一定义 */
}

/* 侧边栏头部图标 - 确保暗黑主题下的一致性 */
body.dark-theme #sidebar-header h2::before {
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3)); /* 暗黑主题下更深的阴影 */
}

body.dark-theme #sidebar-close-button {
    color: var(--text-tertiary);
    background: transparent;
    border: 1px solid transparent;
    border-radius: var(--radius-full);
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--duration-fast) var(--ease-out);
}

/* 侧边栏关闭按钮悬停效果已迁移到 design-system.css 统一管理 */

/* 侧边栏控制区域 */
body.dark-theme #sidebar-controls {
    background: linear-gradient(135deg,
        var(--bg-tertiary) 0%,
        var(--bg-secondary) 100%);
    border-bottom: 1px solid var(--border-primary);
    padding: var(--spacing-md) var(--spacing-lg);
}

/* 搜索输入框 */
body.dark-theme #session-search-input {
    background: linear-gradient(135deg,
        var(--bg-elevated) 0%,
        var(--bg-tertiary) 100%);
    border: 1px solid var(--border-secondary);
    color: var(--text-primary);
    border-radius: var(--radius-md);
    padding: var(--spacing-sm) var(--spacing-md);
    transition: all var(--duration-fast) var(--ease-out);
}

body.dark-theme #session-search-input:focus {
    border-color: var(--focus-border-color);
    background: linear-gradient(135deg,
        var(--bg-hover) 0%,
        var(--bg-elevated) 100%);
    box-shadow: 0 0 0 var(--focus-glow-radius) var(--focus-glow-color);
    outline: none;
    transition: var(--focus-transition);
}

body.dark-theme #session-search-input::placeholder {
    color: var(--text-muted);
    opacity: 0.8;
    font-style: italic;
}

/* 侧边栏按钮 - 暗黑主题适配（简化版，继承明亮主题统一定义） */
body.dark-theme .sidebar-io-button {
    background: linear-gradient(135deg,
        rgba(51, 65, 85, 0.9) 0%,
        rgba(30, 41, 59, 0.95) 100%);
    border-color: var(--border-secondary);
    color: var(--text-primary);
    /* 其他样式（圆角、内边距、字体、过渡、阴影等）继承明亮主题的统一定义 */
}

body.dark-theme .sidebar-io-button:hover {
    background: linear-gradient(135deg,
        rgba(71, 85, 105, 0.9) 0%,
        rgba(51, 65, 85, 0.95) 100%);
    border-color: var(--border-focus);
}

body.dark-theme .sidebar-io-button:active {
    background: linear-gradient(135deg,
        rgba(30, 41, 59, 0.95) 0%,
        rgba(15, 23, 42, 0.98) 100%);
    border-color: var(--border-tertiary);
}

/* Agent List - 简化版，继承明亮主题的统一定义 */
body.dark-theme #agent-list {
    background: rgba(255, 255, 255, 0.02);
    /* border-top, padding-top, margin-top 继承明亮主题定义 */
}

body.dark-theme #agent-list-header {
    color: var(--text-secondary);
    font-weight: var(--font-weight-medium); /* 与明亮主题保持一致 */
    /* padding 继承明亮主题的统一定义：var(--spacing-sm) var(--spacing-lg) */
}

/* 智能体创建按钮暗黑主题适配 - 简化版，继承明亮主题的统一标准 */
body.dark-theme #create-agent-button {
    color: var(--accent-blue);
    /* 其他样式继承明亮主题的统一定义 */
}

body.dark-theme #create-agent-button:hover {
    background: rgba(96, 165, 250, 0.1);
    border-color: rgba(96, 165, 250, 0.3);
    color: var(--accent-blue);
    /* 继承统一的悬停位移效果 */
}

/* === 4. 会话列表暗黑主题适配 - 极简版，继承明亮主题的渐变设计 === */
/* 基础会话项样式 - 只定义暗黑主题的颜色差异 */
body.dark-theme .session-item {
    /* 暗黑主题的背景渐变 - 与明亮主题保持相同的渐变结构 */
    background: linear-gradient(135deg,
        rgba(51, 65, 85, 0.9) 0%,
        rgba(30, 41, 59, 0.95) 100%);
    border-color: var(--border-secondary);
    color: var(--text-primary);
    /* 阴影、布局、交互等样式完全继承明亮主题的统一定义 */
}

/* 会话项悬停状态 - 只定义暗黑主题的颜色差异 */
body.dark-theme .session-item:hover {
    background: linear-gradient(135deg,
        rgba(71, 85, 105, 0.9) 0%,
        rgba(51, 65, 85, 0.95) 100%);
    border-color: var(--border-focus);
    color: var(--text-primary);
    /* 阴影、变换等效果继承明亮主题的统一定义 */
}

/* 会话项选中状态 - 只定义暗黑主题的颜色差异 */
body.dark-theme .session-item.active {
    background: linear-gradient(135deg,
        rgba(59, 130, 246, 0.25) 0%,
        rgba(37, 99, 235, 0.2) 100%);
    border-color: var(--color-primary);
    color: var(--accent-blue);
    /* 字体权重、阴影等样式继承明亮主题的统一定义 */
}

/* 选中状态的悬停效果 - 只定义暗黑主题的颜色差异 */
body.dark-theme .session-item.active:hover {
    background: linear-gradient(135deg,
        rgba(59, 130, 246, 0.3) 0%,
        rgba(37, 99, 235, 0.25) 100%);
    border-color: var(--color-primary-hover);
    /* 阴影、变换等效果继承明亮主题的统一定义 */
}

/* 会话项激活状态 - 只定义暗黑主题的颜色差异 */
body.dark-theme .session-item:active {
    background: linear-gradient(135deg,
        rgba(30, 41, 59, 0.95) 0%,
        rgba(15, 23, 42, 0.98) 100%);
    border-color: var(--border-tertiary);
    /* 变换、阴影等效果继承明亮主题的统一定义 */
}

/* 会话项聚焦状态 - 与统一按钮系统保持一致 */
body.dark-theme .session-item:focus-visible {
    outline: 2px solid var(--focus-border-color);
    outline-offset: 2px;
    box-shadow: 0 0 0 var(--focus-glow-radius) var(--focus-glow-color);
    transition: var(--focus-transition);
}

/* 智能体项特殊样式 - 只定义暗黑主题的颜色差异 */
body.dark-theme .session-item.agent-item {
    background: linear-gradient(135deg,
        rgba(139, 92, 246, 0.15) 0%,
        rgba(124, 58, 237, 0.1) 100%);
    border-left-color: var(--accent-purple);
    /* 内边距、其他样式继承明亮主题的统一定义 */
}

/* 智能体项悬停状态 - 只定义暗黑主题的颜色差异 */
body.dark-theme .session-item.agent-item:hover {
    background: linear-gradient(135deg,
        rgba(139, 92, 246, 0.2) 0%,
        rgba(124, 58, 237, 0.15) 100%);
    border-left-color: var(--accent-purple);
}

/* 智能体项选中状态 - 只定义暗黑主题的颜色差异 */
body.dark-theme .session-item.agent-item.active {
    background: linear-gradient(135deg,
        rgba(139, 92, 246, 0.25) 0%,
        rgba(124, 58, 237, 0.2) 100%);
    border-left-color: var(--accent-purple);
    color: var(--accent-purple);
}

/* 智能体项选中悬停状态 - 只定义暗黑主题的颜色差异 */
body.dark-theme .session-item.agent-item.active:hover {
    background: linear-gradient(135deg,
        rgba(139, 92, 246, 0.3) 0%,
        rgba(124, 58, 237, 0.25) 100%);
}

/* 会话删除按钮暗黑主题适配 - 使用正确的DOM路径和最高优先级 */
body.dark-theme #app-container #sidebar #session-list .session-item .session-delete-button {
    color: var(--text-tertiary);
    /* 使用正确的DOM路径和最高优先级确保精确控制 */
    width: 20px !important;
    height: 20px !important;
    padding: 2px !important;
    border-radius: 2px !important;
    right: 8px !important;
    box-sizing: border-box !important;
    min-width: 20px !important;
    max-width: 20px !important;
    min-height: 20px !important;
    max-height: 20px !important;
}

body.dark-theme #app-container #sidebar #session-list .session-item .session-delete-button:hover {
    background: rgba(239, 68, 68, 0.1); /* 与明亮主题保持一致的透明度 */
    color: var(--color-danger); /* 使用统一的危险色变量 */
    opacity: 1; /* 确保透明度与白天主题一致 */
    border-radius: 2px !important; /* 使用绝对像素值确保精确控制 */
    transform: translateY(-50%); /* 保持垂直居中变换，确保位置一致 */
    right: 8px !important; /* 使用绝对像素值确保精确控制 */
    /* 使用正确的DOM路径和最高优先级确保悬停状态尺寸精确控制 */
    width: 20px !important;
    height: 20px !important;
    padding: 2px !important;
    box-sizing: border-box !important;
    min-width: 20px !important;
    max-width: 20px !important;
    min-height: 20px !important;
    max-height: 20px !important;
}

body.dark-theme .session-delete-button:active {
    background: rgba(239, 68, 68, 0.25); /* 暗黑主题下的红色系激活背景 */
    transform: translateY(-50%); /* 保持垂直居中变换，确保位置一致 */
    border-radius: var(--radius-xs); /* 使用最小圆角（2px），确保背景块精细化，与白天主题完全一致 */
    right: var(--spacing-sm); /* 明确定义定位，确保与基础样式一致 */
}

/* 选中状态的删除按钮 - 只定义颜色差异 */
body.dark-theme .session-item.active .session-delete-button {
    color: var(--accent-blue);
    /* 透明度等样式继承明亮主题的统一定义 */
}

body.dark-theme .session-item.active .session-delete-button:hover {
    color: var(--color-danger);
    background: rgba(239, 68, 68, 0.1); /* 与明亮主题保持一致的透明度 */
    opacity: 1; /* 确保透明度与白天主题一致 */
    border-radius: var(--radius-xs); /* 使用最小圆角（2px），确保背景块精细化，与白天主题完全一致 */
    transform: translateY(-50%); /* 保持垂直居中变换，确保位置一致 */
    right: var(--spacing-sm); /* 明确定义定位，确保与基础样式一致 */
}

/* 智能体项删除按钮悬停效果 - 与普通删除按钮保持一致 */
body.dark-theme .session-item.agent-item .session-delete-button:hover {
    background: rgba(239, 68, 68, 0.1); /* 与明亮主题保持一致的透明度 */
    color: var(--color-danger); /* 使用统一的危险色变量 */
    opacity: 1; /* 确保透明度与白天主题一致 */
    border-radius: var(--radius-xs); /* 使用最小圆角（2px），确保背景块精细化，与白天主题完全一致 */
    transform: translateY(-50%); /* 保持垂直居中变换，确保位置一致 */
    right: var(--spacing-sm); /* 明确定义定位，确保与基础样式一致 */
}

/* 智能体项选中状态下的删除按钮 - 只定义颜色差异 */
body.dark-theme .session-item.agent-item.active .session-delete-button {
    color: var(--accent-purple);
    /* 透明度等样式继承明亮主题的统一定义 */
}

/* 智能体项选中状态删除按钮悬停效果 */
body.dark-theme .session-item.agent-item.active .session-delete-button:hover {
    background: rgba(239, 68, 68, 0.1); /* 与明亮主题保持一致的透明度 */
    color: var(--color-danger); /* 使用统一的危险色变量 */
    opacity: 1; /* 确保透明度与白天主题一致 */
    border-radius: var(--radius-xs); /* 使用最小圆角（2px），确保背景块精细化，与白天主题完全一致 */
    transform: translateY(-50%); /* 保持垂直居中变换，确保位置一致 */
    right: var(--spacing-sm); /* 明确定义定位，确保与基础样式一致 */
}

/* 侧边栏遮罩 */
body.dark-theme #sidebar-overlay {
    background-color: rgba(0, 0, 0, 0.7) !important;
    backdrop-filter: blur(4px) !important;
}

/* === Header区域一致性修复 === */
/* 统一Header导航容器间距 */
body.dark-theme #top-actions {
    gap: 5.625px !important; /* 统一为白天主题的间距值，保持视觉平衡 */
}

/* === 界面元素尺寸统一 === */
/* 工具栏按钮尺寸 - 与白天主题保持一致 */
body.dark-theme .toolbar-button {
    width: 36px;
    height: 36px;
}

/* 发送按钮尺寸 - 与白天主题保持一致 */
body.dark-theme #send-button {
    width: 40px;
    height: 40px;
}

/* 消息操作按钮内边距 - 与白天主题保持一致 */
body.dark-theme .message-action-button,
body.dark-theme .message-container button {
    padding: var(--spacing-sm) var(--spacing-md);
}

/* === 响应式布局 - 与白天主题保持一致 === */
@media (max-width: 1024px) and (min-width: 769px) {
    body.dark-theme .toolbar-button {
        width: 34px;
        height: 34px;
    }

    body.dark-theme #send-button {
        width: 38px;
        height: 38px;
    }
}

@media (max-width: 768px) {
    body.dark-theme .toolbar-button {
        width: 32px;
        height: 32px;
    }

    body.dark-theme #send-button {
        width: 36px;
        height: 36px;
    }
}

@media (max-width: 480px) {
    body.dark-theme .toolbar-button {
        width: 28px;
        height: 28px;
    }

    body.dark-theme #send-button {
        width: 32px;
        height: 32px;
    }
}

/* === 侧边栏样式 - 与白天主题保持一致 === */
/* 搜索输入框样式 */
body.dark-theme #sidebar input[type="search"] {
    padding-left: 36px; /* 为搜索图标留出空间 */
    border-radius: var(--radius-xl); /* 现代化外观 */
}

/* 对话删除按钮 */
body.dark-theme #sidebar nav > div button {
    width: 28px;
    height: 28px;
    padding: var(--spacing-sm);
    opacity: 0.7;
    transition: all var(--duration-fast) var(--ease-out);
}

/* 创建智能体按钮 */
body.dark-theme #sidebar h3 + button {
    width: 28px;
    height: 28px;
    padding: var(--spacing-sm);
}

/* 侧边栏过渡动画 */
body.dark-theme #sidebar {
    transition: transform var(--duration-fast) var(--ease-out);
}

/* 侧边栏关闭按钮过渡动画 */
body.dark-theme #sidebar h2 + button {
    transition: all var(--duration-fast) var(--ease-out);
}

/* === 侧边栏响应式布局 - 与白天主题保持一致 === */
@media (max-width: 768px) {
    body.dark-theme #sidebar {
        width: 320px;
    }

    body.dark-theme #sidebar nav > div button,
    body.dark-theme #sidebar h3 + button {
        width: 24px;
        height: 24px;
        padding: var(--spacing-xs);
    }
}

@media (max-width: 480px) {
    body.dark-theme #sidebar {
        width: 90%;
        max-width: 340px;
        /* 与白天主题保持一致的移动端适配 */
        top: 60px;
        height: calc(100vh - 60px);
        border-radius: 0 var(--radius-lg) var(--radius-lg) 0;
        box-shadow: var(--shadow-xl);
    }

    body.dark-theme #sidebar input[type="search"] {
        padding-left: 32px;
    }

    /* 顶部栏在暗黑主题下的固定样式 */
    body.dark-theme #top-bar {
        background: var(--bg-primary);
        border-bottom: 1px solid var(--border-primary);
        backdrop-filter: blur(10px);
    }

    /* 侧边栏遮罩层在暗黑主题下的样式 */
    body.dark-theme #sidebar.open::before {
        background: rgba(0, 0, 0, 0.6);
        backdrop-filter: blur(3px);
    }

    /* 汉堡键在移动端暗黑主题下的优化 */
    body.dark-theme #menu-toggle-button {
        background: var(--bg-secondary);
        border: 1px solid var(--border-secondary);
    }

    body.dark-theme #menu-toggle-button:hover {
        background: var(--bg-tertiary);
        border-color: var(--border-tertiary);
    }
}

/* === 5. 输入区域系统 - 优化版 === */
/* 输入容器 */
body.dark-theme #input-container {
    background: var(--bg-overlay);
    backdrop-filter: var(--blur-md);
    box-shadow: var(--shadow-lg);
    position: relative;
    padding: var(--spacing-lg); /* 与白天主题保持一致 */
}

/* 输入框容器 */
body.dark-theme #input-textarea-wrapper {
    background: var(--input-bg);
    backdrop-filter: var(--blur-md);
    border: 1px solid var(--input-border);
    border-radius: var(--radius-2xl); /* 与白天主题保持一致 */
    box-shadow: var(--shadow-sm);

    /* 统一过渡动画 */
    transition:
        border-color var(--duration-fast) var(--ease-out),
        box-shadow var(--duration-fast) var(--ease-out);

    /* 基础布局属性 */
    position: relative;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    z-index: var(--z-content);
}

/* 聚焦状态 - 统一现代化聚焦效果 */
body.dark-theme #input-textarea-wrapper:focus-within {
    border-color: var(--focus-border-color);
    border-width: 1.5px;
    background: linear-gradient(135deg,
        rgba(30, 41, 59, 0.98) 0%,
        rgba(15, 23, 42, 0.95) 100%);
    /* 统一的聚焦阴影效果 */
    box-shadow:
        0 4px 12px rgba(0, 0, 0, 0.15),
        0 0 0 2px var(--focus-glow-color);
    /* 优化边框渲染 */
    border-style: solid;
    box-sizing: border-box;
}

/* 移除复杂的发光效果伪元素 - 简化设计 */

/* 移除复杂的底部过渡效果 - 简化设计 */

/* 移除装饰性动画 - 简化设计，仅保留必要的交互反馈 */

/* 消息输入框 - 优化版 */
body.dark-theme #message-input {
    background: transparent;
    border: none;
    color: var(--text-primary);
    padding: 11.25px 15px;
    outline: none;
    resize: none;
}

body.dark-theme #message-input::placeholder {
    color: var(--text-muted);
    opacity: 0.8;
    font-style: italic;
}

/* 其他输入框占位符样式统一 */
body.dark-theme #message-search-input::placeholder,
body.dark-theme .image-gen-textarea::placeholder,
body.dark-theme .prompt-optimization-input::placeholder,
body.dark-theme input::placeholder,
body.dark-theme textarea::placeholder {
    color: var(--text-muted) !important;
    opacity: 0.8 !important;
    font-style: italic !important;
}

/* 输入工具栏 - 使用设计系统变量 */
body.dark-theme #input-toolbar {
    /* 使用设计系统变量 */
    border-top: 1px solid var(--toolbar-border);
    background: var(--toolbar-bg);
    padding: var(--toolbar-padding);
    min-height: var(--toolbar-height);

    /* 基础布局属性 */
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    z-index: var(--z-elevated);
    overflow: hidden;
}

/* 移除复杂的工具栏悬停效果 - 简化设计 */

/* 移除复杂的工具栏伪元素过渡效果 - 简化设计 */

/* 工具栏按钮样式 - 现代极简一体化设计 */
body.dark-theme .input-action-button,
body.dark-theme .toolbar-button {
    /* 现代极简一体化设计 - 与底部背景更好融合 */
    background: rgba(51, 65, 85, 0.3);
    border: 1px solid rgba(71, 85, 105, 0.2);
    color: var(--text-tertiary);
    border-radius: var(--button-radius);
    padding: var(--button-padding);
    /* 移除复杂的backdrop-filter和阴影，实现一体化效果 */
    backdrop-filter: none;
    box-shadow: none;
    transition: var(--button-transition);
    position: relative;
    /* 移除overflow: hidden，简化设计 */
}

/* 工具栏按钮悬停效果已迁移到 design-system.css 统一管理 */

/* 移除复杂的按钮发光效果伪元素 - 简化设计 */

/* 按钮激活效果 - 简化 */
body.dark-theme .input-action-button:active,
body.dark-theme .toolbar-button:active {
    /* 移除动效：transform: scale(0.98); */
    box-shadow: var(--shadow-xs);
}

/* 按钮聚焦效果 - 使用设计系统 */
body.dark-theme .input-action-button:focus-visible,
body.dark-theme .toolbar-button:focus-visible {
    outline: 2px solid var(--border-focus);
    outline-offset: 2px;
}

/* 移除按钮发光动画 - 简化设计 */

/* === 8. 消息区域系统 - 现代极简设计 === */
body.dark-theme #messages-container {
    /* 使用设计系统变量 */
    border-bottom: none;
    margin-bottom: 0;
    position: relative;
    background: var(--bg-primary);
    color: var(--text-primary);
    backdrop-filter: var(--blur-md);
}

/* 添加消息区域底部渐变遮罩，实现与输入区域的柔和过渡 */
body.dark-theme #messages-container::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 32px;
    background: linear-gradient(to bottom,
        transparent 0%,
        rgba(15, 23, 42, 0.1) 25%,
        rgba(15, 23, 42, 0.3) 50%,
        rgba(15, 23, 42, 0.6) 75%,
        rgba(15, 23, 42, 0.9) 100%);
    pointer-events: none;
    z-index: 1;
}

/* === 清理规则：移除分隔线和边框类（已整合到主要样式中） === */
/* 这些规则已经整合到上面的主要样式定义中，无需重复定义 */

/* === 6. 发送按钮优化 === */
body.dark-theme #send-button:not(.abort-active) {
    background: linear-gradient(135deg,
        var(--color-primary) 0%,
        var(--color-primary-hover) 50%,
        var(--color-primary-active) 100%);
    border: 1px solid var(--color-primary);
    color: var(--text-inverse);
    box-shadow: var(--shadow-button);
    transition: all var(--duration-fast) var(--ease-out);
    border-radius: var(--radius-lg);
    font-weight: var(--font-weight-semibold);
    width: 40px;
    height: 40px;
    min-width: 40px;
    min-height: 40px;
}

body.dark-theme #send-button:hover:not(:disabled):not(.abort-active) {
    background: linear-gradient(135deg,
        var(--color-primary-hover) 0%,
        var(--color-primary-active) 50%,
        var(--color-primary-dark) 100%);
    border-color: var(--color-primary-hover);
    /* 移除动效：transform: translateY(-2px) scale(1.05); */
    box-shadow: var(--shadow-button-hover);
}

body.dark-theme #send-button.abort-active {
    background: linear-gradient(135deg,
        var(--color-danger) 0%,
        var(--color-danger-hover) 50%,
        var(--color-danger-active) 100%);
    border-color: var(--color-danger);
    color: var(--text-inverse);
    box-shadow: var(--shadow-button-danger);
}

body.dark-theme #send-button.abort-active:hover:not(:disabled) {
    background: linear-gradient(135deg,
        var(--color-danger-hover) 0%,
        var(--color-danger-active) 50%,
        var(--color-danger-dark) 100%);
    border-color: var(--color-danger-hover);
    /* 移除动效：transform: translateY(-2px) scale(1.05); */
    box-shadow: var(--shadow-button-danger-hover);
}

body.dark-theme #send-button:disabled {
    background: linear-gradient(135deg,
        #475569 0%,
        #64748b 100%) !important;
    border-color: #475569 !important;
    color: #94a3b8 !important;
    cursor: not-allowed !important;
    opacity: 0.6 !important;
    transform: none !important;
    box-shadow: none !important;
}

body.dark-theme #send-button:focus-visible {
    outline: 3px solid rgba(59, 130, 246, 0.5) !important;
    outline-offset: 2px !important;
}

body.dark-theme #send-button:active:not(:disabled) {
    /* 移除动效：transform: translateY(0) scale(0.95) !important; */
    box-shadow:
        0 2px 6px rgba(59, 130, 246, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
}

/* 发送按钮图标和文本 */
body.dark-theme #send-button i,
body.dark-theme #send-button .fa,
body.dark-theme #send-button svg,
body.dark-theme #send-button span {
    color: white !important;
    transition: transform var(--duration-fast) var(--ease-out) !important;
}

/* 发送按钮图标悬停动效已移除 */

/* === 7. 欢迎界面适配 === */
body.dark-theme #empty-state-active .welcome-title {
    color: var(--text-primary) !important;
    font-weight: var(--font-weight-semibold) !important;
}

body.dark-theme #empty-state-active .model-name-display {
    color: var(--text-secondary) !important;
    font-weight: var(--font-weight-medium) !important;
}

body.dark-theme #empty-state-active .welcome-message {
    color: var(--text-secondary) !important;
    line-height: var(--line-height-relaxed) !important;
}

/* 欢迎建议按钮 - 暗黑主题适配（简化版，继承明亮主题统一定义） */
body.dark-theme #empty-state-active .suggestion-button,
body.dark-theme .prompt-suggestion,
body.dark-theme .suggestion-item,
body.dark-theme .welcome-suggestion {
    background: linear-gradient(135deg,
        rgba(51, 65, 85, 0.9) 0%,
        rgba(30, 41, 59, 0.95) 100%);
    border-color: var(--border-secondary);
    color: var(--text-primary);
    backdrop-filter: blur(8px);
    /* 其他样式（圆角、内边距、字体、过渡、阴影、最小高度、行高等）继承明亮主题的统一定义 */
}

body.dark-theme #empty-state-active .suggestion-button:hover,
body.dark-theme .prompt-suggestion:hover,
body.dark-theme .suggestion-item:hover,
body.dark-theme .welcome-suggestion:hover {
    background: linear-gradient(135deg,
        rgba(71, 85, 105, 0.9) 0%,
        rgba(51, 65, 85, 0.95) 100%);
    border-color: var(--border-focus);
}

body.dark-theme #empty-state-active .suggestion-button:active,
body.dark-theme .prompt-suggestion:active,
body.dark-theme .suggestion-item:active,
body.dark-theme .welcome-suggestion:active {
    background: linear-gradient(135deg,
        rgba(30, 41, 59, 0.95) 0%,
        rgba(15, 23, 42, 0.98) 100%);
    border-color: var(--border-tertiary);
}

/* === 8. 消息区域适配（已合并到上面的消息区域定义中） === */
/* 消息区域样式已在第990行统一定义，避免重复 */

/* 用户消息气泡 - 增强光影质感 */
body.dark-theme .message-container.user-message-container .message-bubble {
    background:
        linear-gradient(135deg,
            rgba(99, 102, 241, 0.18) 0%,
            rgba(139, 92, 246, 0.22) 25%,
            rgba(59, 130, 246, 0.15) 50%,
            rgba(99, 102, 241, 0.20) 75%,
            rgba(139, 92, 246, 0.16) 100%) !important;

    /* 增强玻璃质感 */
    backdrop-filter: blur(16px) saturate(1.3) brightness(1.1) !important;
    border: none !important;

    /* 深度阴影系统 */
    box-shadow:
        /* 内部高光 */
        inset 0 1px 0 rgba(255, 255, 255, 0.15),
        inset 0 -1px 0 rgba(0, 0, 0, 0.1),
        /* 外部阴影 */
        0 8px 32px rgba(99, 102, 241, 0.15),
        0 4px 16px rgba(0, 0, 0, 0.2),
        0 2px 8px rgba(0, 0, 0, 0.15),
        /* 微妙边框发光 */
        0 0 0 1px rgba(99, 102, 241, 0.2) !important;

    /* 平滑过渡 */
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1) !important;
    position: relative !important;
    overflow: hidden !important;
}

/* 用户消息气泡悬停效果 */
body.dark-theme .message-container.user-message-container .message-bubble:hover {
    background:
        linear-gradient(135deg,
            rgba(99, 102, 241, 0.25) 0%,
            rgba(139, 92, 246, 0.28) 25%,
            rgba(59, 130, 246, 0.22) 50%,
            rgba(99, 102, 241, 0.26) 75%,
            rgba(139, 92, 246, 0.23) 100%) !important;

    box-shadow:
        inset 0 1px 0 rgba(255, 255, 255, 0.2),
        inset 0 -1px 0 rgba(0, 0, 0, 0.08),
        0 12px 40px rgba(99, 102, 241, 0.2),
        0 6px 20px rgba(0, 0, 0, 0.25),
        0 0 0 1px rgba(99, 102, 241, 0.3) !important;

    transform: translateY(-1px) !important;
}

/* AI消息气泡 - 增强光影质感 */
body.dark-theme .message-container.assistant-message-container .message-bubble {
    background:
        linear-gradient(135deg,
            rgba(30, 41, 59, 0.9) 0%,
            rgba(51, 65, 85, 0.95) 25%,
            rgba(71, 85, 105, 0.8) 50%,
            rgba(30, 41, 59, 0.92) 75%,
            rgba(51, 65, 85, 0.88) 100%) !important;

    /* 增强玻璃质感 */
    backdrop-filter: blur(20px) saturate(1.2) brightness(1.05) !important;
    border: none !important;

    /* 深度阴影系统 */
    box-shadow:
        /* 内部高光 */
        inset 0 1px 0 rgba(255, 255, 255, 0.08),
        inset 0 -1px 0 rgba(0, 0, 0, 0.15),
        /* 外部阴影 */
        0 8px 32px rgba(0, 0, 0, 0.25),
        0 4px 16px rgba(0, 0, 0, 0.2),
        0 2px 8px rgba(0, 0, 0, 0.15),
        /* 微妙边框 */
        0 0 0 1px rgba(255, 255, 255, 0.05) !important;

    /* 平滑过渡 */
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1) !important;
    position: relative !important;
    overflow: hidden !important;
}

/* AI消息气泡悬停效果 */
body.dark-theme .message-container.assistant-message-container .message-bubble:hover {
    background:
        linear-gradient(135deg,
            rgba(30, 41, 59, 0.95) 0%,
            rgba(51, 65, 85, 1) 25%,
            rgba(71, 85, 105, 0.9) 50%,
            rgba(30, 41, 59, 0.98) 75%,
            rgba(51, 65, 85, 0.93) 100%) !important;

    box-shadow:
        inset 0 1px 0 rgba(255, 255, 255, 0.12),
        inset 0 -1px 0 rgba(0, 0, 0, 0.1),
        0 12px 40px rgba(0, 0, 0, 0.3),
        0 6px 20px rgba(0, 0, 0, 0.25),
        0 0 0 1px rgba(255, 255, 255, 0.08) !important;

    transform: translateY(-1px) !important;
}

/* 消息操作按钮 */
body.dark-theme .message-action-button {
    background: rgba(255, 255, 255, 0.08) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    color: var(--text-tertiary) !important;
    border-radius: var(--radius-sm) !important;
    padding: var(--spacing-xs) var(--spacing-sm) !important;
    transition: all var(--duration-fast) var(--ease-out) !important;
    backdrop-filter: blur(8px) !important;
}

body.dark-theme .message-action-button:hover {
    background: rgba(255, 255, 255, 0.15) !important;
    border-color: rgba(255, 255, 255, 0.2) !important;
    color: var(--text-primary) !important;
    transform: translateY(-1px) !important;
    box-shadow: var(--shadow-sm) !important;
}

/* === 9. 模态框和表单 === */
body.dark-theme .modal-content {
    background: linear-gradient(135deg,
        rgba(30, 41, 59, 0.95) 0%,
        rgba(15, 23, 42, 0.98) 100%) !important;
    border: 1px solid var(--border-primary) !important;
    border-radius: var(--radius-xl) !important;
    box-shadow: var(--shadow-2xl) !important;
    backdrop-filter: blur(20px) saturate(1.2) !important;
}

/* === 表单系统 - 简化版，继承明亮主题的统一定义 === */
body.dark-theme .form-group label {
    color: var(--text-secondary);
    font-weight: var(--font-weight-medium);
    /* 其他样式继承明亮主题的统一定义 */
}

body.dark-theme .form-group input[type="text"],
body.dark-theme .form-group input[type="password"],
body.dark-theme .form-group input[type="number"],
body.dark-theme .form-group textarea,
body.dark-theme .form-group select {
    background: linear-gradient(135deg,
        rgba(71, 85, 105, 0.9) 0%,
        rgba(51, 65, 85, 0.95) 100%);
    border-color: var(--border-secondary);
    color: var(--text-primary);
    /* 圆角、过渡等样式继承明亮主题的统一定义 */
}

body.dark-theme .form-group input:focus,
body.dark-theme .form-group textarea:focus,
body.dark-theme .form-group select:focus {
    border-color: var(--focus-border-color);
    background: linear-gradient(135deg,
        rgba(129, 199, 132, 0.08) 0%,
        rgba(71, 85, 105, 0.95) 100%);
    box-shadow: 0 0 0 var(--focus-glow-radius) var(--focus-glow-color);
    /* 轮廓和过渡样式继承明亮主题的统一定义 */
}

/* === 按钮系统 - 简化版，继承明亮主题的统一定义 === */
body.dark-theme .btn-primary {
    background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-hover) 100%);
    border-color: var(--color-primary);
    color: white;
    /* 圆角、字体、过渡等样式继承明亮主题的统一定义 */
}

body.dark-theme .btn-primary:hover:not(:disabled) {
    background: linear-gradient(135deg, var(--color-primary-hover) 0%, var(--color-primary-active) 100%);
    border-color: var(--color-primary-hover);
    /* 变换和阴影效果继承明亮主题的统一定义 */
}

body.dark-theme .btn-secondary {
    background: linear-gradient(135deg, var(--color-gray-100) 0%, var(--color-gray-200) 100%);
    border-color: var(--border-secondary);
    color: var(--text-primary);
    /* 圆角、字体、过渡等样式继承明亮主题的统一定义 */
}

body.dark-theme .btn-secondary:hover:not(:disabled) {
    background: linear-gradient(135deg, var(--color-gray-200) 0%, var(--color-gray-300) 100%);
    border-color: var(--border-primary);
    /* 变换和阴影效果继承明亮主题的统一定义 */
}

/* === 11. Mermaid图形系统 - 暗黑主题优化版 === */

/* Mermaid容器基础样式 - 使用设计系统变量 */
body.dark-theme .mermaid-container {
    position: relative;
    margin: var(--mermaid-container-margin);
}

/* Mermaid图形视图 - 暗黑主题优化版 */
body.dark-theme .mermaid-diagram-view {
    text-align: center;
    background: linear-gradient(135deg,
        var(--bg-secondary) 0%,
        var(--bg-tertiary) 100%);
    color: var(--text-secondary);
    padding: var(--mermaid-diagram-padding);
    border-radius: var(--mermaid-diagram-border-radius);
    border: 1px solid var(--border-primary);
    min-height: var(--mermaid-diagram-min-height);
    display: block;
    overflow: auto;
    transition: all var(--duration-normal) var(--ease-out);
    box-shadow: var(--shadow-sm);
}

/* Mermaid图形视图悬停效果 */
body.dark-theme .mermaid-diagram-view:hover {
    background: linear-gradient(135deg,
        var(--bg-tertiary) 0%,
        var(--bg-secondary) 100%);
    border-color: var(--border-secondary);
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

/* Mermaid SVG元素适配 */
body.dark-theme .mermaid-diagram-view svg {
    max-width: 100%;
    height: auto;
    display: block;
    margin: 0 auto;
    filter: brightness(1.1) contrast(1.05) !important;
}

/* Mermaid操作按钮容器 - 使用设计系统变量 */
body.dark-theme .mermaid-actions {
    position: absolute;
    top: var(--mermaid-actions-position-top);
    right: var(--mermaid-actions-position-right);
    display: flex;
    gap: var(--mermaid-action-button-gap);
    opacity: 0;
    /* 移除渐变过渡：transition: opacity var(--duration-normal) var(--ease-out); */
    pointer-events: none;
    z-index: var(--z-floating);
}

body.dark-theme .mermaid-container:hover .mermaid-actions,
body.dark-theme .mermaid-container.actions-visible .mermaid-actions {
    opacity: 1;
    pointer-events: auto;
}

/* Mermaid操作按钮 - 优化版 */
body.dark-theme .mermaid-action-button {
    background: var(--bg-overlay);
    border: 1px solid var(--border-primary);
    color: var(--text-secondary);
    border-radius: var(--radius-sm);
    padding: var(--mermaid-action-button-padding);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    transition: all var(--duration-fast) var(--ease-out);
    backdrop-filter: var(--blur-sm);
    cursor: pointer;
}

body.dark-theme .mermaid-action-button:hover {
    background: var(--button-hover-bg);
    border-color: var(--border-focus);
    color: var(--text-primary);
    /* 移除动效：transform: translateY(-1px); */
    box-shadow: var(--shadow-sm);
}

body.dark-theme .mermaid-action-button.active {
    background: var(--button-active-bg);
    border-color: var(--color-primary);
    color: var(--accent-blue);
}

/* Mermaid代码视图 - 优化版 */
body.dark-theme .mermaid-code-view {
    display: none;
    background: linear-gradient(135deg,
        var(--bg-code) 0%,
        var(--bg-secondary) 100%);
    color: var(--text-code);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-primary);
    margin: var(--spacing-lg) 0;
    overflow: hidden;
    box-shadow: var(--shadow-md);
    flex-direction: column;
    transition: all var(--duration-normal) var(--ease-out);
}

body.dark-theme .mermaid-code-view.visible {
    display: flex;
}

/* Mermaid代码视图pre元素 - 优化版 */
body.dark-theme .mermaid-code-view pre,
body.dark-theme .mermaid-container .mermaid-code-view pre {
    margin: 0;
    padding: var(--spacing-lg) var(--spacing-xl);
    white-space: pre-wrap;
    word-break: break-all;
    font-family: var(--font-family-mono);
    font-size: var(--font-size-sm);
    line-height: var(--line-height-relaxed);
    max-height: 400px;
    overflow-y: auto;
    background: transparent;
    border-radius: 0;
    border: none;
    box-shadow: none;
    color: var(--text-code);
}

/* Mermaid加载和占位符状态 - 优化版 */
body.dark-theme .mermaid-loading,
body.dark-theme .mermaid-placeholder {
    min-height: var(--mermaid-diagram-min-height);
    background: linear-gradient(135deg,
        var(--bg-secondary) 0%,
        var(--bg-tertiary) 100%);
    border: 1px dashed var(--border-secondary);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-muted);
    font-style: italic;
    margin: var(--mermaid-container-margin);
    padding: var(--spacing-xl);
    box-sizing: border-box;
    text-align: center;
    box-shadow:
        0 2px 8px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.03) !important;
    font-size: 0.9em;
}

/* Mermaid错误状态 */
body.dark-theme .mermaid.mermaid-error {
    background: linear-gradient(135deg,
        rgba(127, 29, 29, 0.2) 0%,
        rgba(153, 27, 27, 0.15) 100%) !important;
    border: 1px solid rgba(239, 68, 68, 0.3) !important;
    color: #fca5a5 !important;
    padding: 20px !important;
    text-align: left;
    border-radius: 12px !important;
    box-shadow:
        0 4px 12px rgba(239, 68, 68, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.05) !important;
    font-size: 0.9em;
}

/* Mermaid错误消息 */
body.dark-theme .mermaid-error-message {
    font-weight: 600 !important;
    margin-bottom: 10px !important;
    display: block;
    font-size: 0.9em;
    color: #fca5a5 !important;
}

/* Mermaid错误代码显示 */
body.dark-theme .mermaid-error .mermaid-code-display pre {
    background: rgba(30, 41, 59, 0.8) !important;
    border: 1px dashed var(--border-tertiary) !important;
    color: var(--text-secondary) !important;
    padding: 16px !important;
    border-radius: 8px !important;
    text-align: left;
    white-space: pre-wrap;
    word-break: break-all;
    font-size: 0.85em;
    max-height: 200px;
    overflow-y: auto;
    margin-top: 10px !important;
    font-family: 'JetBrains Mono', 'Fira Code', 'Cascadia Code', Consolas, Monaco, monospace !important;
}

/* Mermaid渲染状态 */
body.dark-theme .mermaid-rendering {
    opacity: 0.7;
    position: relative;
}

body.dark-theme .mermaid-rendering::after {
    content: '渲染中...';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(30, 41, 59, 0.9) !important;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9em;
    color: var(--text-muted) !important;
    z-index: 10;
    border-radius: 12px;
}

/* Mermaid待处理状态 */
body.dark-theme .mermaid-pending {
    background: linear-gradient(135deg,
        rgba(30, 41, 59, 0.6) 0%,
        rgba(51, 65, 85, 0.4) 100%) !important;
    border: 1px solid var(--border-tertiary) !important;
    border-radius: 12px !important;
    padding: 24px !important;
    text-align: center;
    color: var(--text-muted) !important;
    font-size: 0.9em;
    min-height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Mermaid成功状态 */
body.dark-theme .mermaid-success {
    opacity: 1 !important;
}

/* Mermaid视图切换状态 */
body.dark-theme .mermaid-container:not(.code-view-active) .mermaid-diagram-view {
    display: block !important;
}

body.dark-theme .mermaid-container:not(.code-view-active) .mermaid-code-view {
    display: none !important;
}

body.dark-theme .mermaid-container.code-view-active .mermaid-diagram-view {
    display: none !important;
}

body.dark-theme .mermaid-container.code-view-active .mermaid-code-view {
    display: flex !important;
}

body.dark-theme .mermaid-diagram-view.hidden {
    display: none !important;
}

/* === 12. 滚动条差异化处理系统 - 黑夜主题 === */

/* 全局滚动条隐藏 - 除主聊天区域外 */
body.dark-theme ::-webkit-scrollbar {
    display: none !important;
}

/* Firefox 全局滚动条隐藏 */
body.dark-theme * {
    scrollbar-width: none !important;
}

/* === 主聊天区域专用滚动条样式 - 黑夜主题 === */
/* 仅主聊天区域保留滚动条，隐藏箭头 */
body.dark-theme #chatbox::-webkit-scrollbar {
    display: block !important;
    width: 14px !important;
}

body.dark-theme #chatbox::-webkit-scrollbar-button {
    display: none !important;
}

body.dark-theme #chatbox::-webkit-scrollbar-track {
    background: transparent !important;
    border-radius: 10px;
}

body.dark-theme #chatbox::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.18) !important;
    border: 3px solid transparent;
    border-radius: 10px;
    min-height: 50px;
    transition: all var(--duration-medium) var(--ease-in-out) !important;
}

body.dark-theme #chatbox::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.28) !important;
    border-width: 2px;
}

body.dark-theme #chatbox::-webkit-scrollbar-thumb:active {
    background: rgba(255, 255, 255, 0.35) !important;
    border-width: 1px;
}

body.dark-theme #chatbox::-webkit-scrollbar-corner {
    background: transparent !important;
}

/* Firefox 主聊天区域滚动条 */
body.dark-theme #chatbox {
    scrollbar-width: thin !important;
    scrollbar-color: rgba(255, 255, 255, 0.18) transparent !important;
}

/* 其他组件滚动条隐藏 - 已通过全局设置处理 */

/* === 13. 滚动按钮系统优化 === */

/* 滚动按钮容器 */
body.dark-theme .scroll-buttons-container {
    position: fixed;
    bottom: 130px;
    right: var(--spacing-lg);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    z-index: calc(var(--z-fixed) - 10);
    opacity: 0.4;
    visibility: visible;
    transition: opacity var(--duration-medium) var(--ease-out),
                bottom var(--duration-medium) var(--ease-in-out) !important;
}

body.dark-theme .scroll-buttons-container:hover,
body.dark-theme .scroll-buttons-container.active {
    opacity: 1;
}

/* 滚动按钮 */
body.dark-theme .scroll-button {
    width: 38px !important;
    height: 38px !important;
    background: linear-gradient(135deg,
        rgba(30, 41, 59, 0.95) 0%,
        rgba(51, 65, 85, 0.9) 100%) !important;
    border: 1px solid var(--border-tertiary) !important;
    color: var(--text-secondary) !important;
    border-radius: var(--radius-full) !important;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg) !important;
    box-shadow:
        0 4px 16px rgba(0, 0, 0, 0.2),
        0 1px 3px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
    transition: all var(--duration-fast) var(--ease-out) !important;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(12px) saturate(1.2) !important;
}

body.dark-theme .scroll-button:hover {
    background: linear-gradient(135deg,
        rgba(59, 130, 246, 0.2) 0%,
        rgba(30, 41, 59, 0.95) 100%) !important;
    border-color: var(--border-secondary) !important;
    color: var(--text-primary) !important;
    box-shadow:
        0 6px 20px rgba(0, 0, 0, 0.25),
        0 2px 6px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.15) !important;
    /* 移除动效：transform: translateY(-2px) scale(1.05) !important; */
}

body.dark-theme .scroll-button:active {
    /* 移除动效：transform: translateY(0) scale(0.95) !important; */
    box-shadow:
        0 2px 8px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.05) !important;
}

body.dark-theme .scroll-button:focus {
    outline: none !important;
}

/* 滚动按钮图标 */
body.dark-theme .scroll-button i,
body.dark-theme .scroll-button .fa {
    transition: transform var(--duration-fast) var(--ease-out) !important;
}

/* 滚动按钮图标悬停动效已移除 */

/* 滚动按钮特殊状态 */
body.dark-theme .scroll-button.scroll-to-top {
    background: linear-gradient(135deg,
        rgba(16, 185, 129, 0.15) 0%,
        rgba(30, 41, 59, 0.95) 100%) !important;
}

body.dark-theme .scroll-button.scroll-to-bottom {
    background: linear-gradient(135deg,
        rgba(59, 130, 246, 0.15) 0%,
        rgba(30, 41, 59, 0.95) 100%) !important;
}

/* 响应式适配 */
@media (max-width: 768px) {
    body.dark-theme .scroll-buttons-container {
        bottom: 120px;
        right: var(--spacing-md);
    }

    body.dark-theme .scroll-button {
        width: 34px !important;
        height: 34px !important;
        font-size: var(--font-size-base) !important;
    }
}

/* === 14. 消息编辑功能优化 === */

/* 消息编辑状态 - 简约清新设计 */
body.dark-theme .message-bubble.editing {
    background: rgba(30, 41, 59, 0.8) !important;
    border: 1px solid rgba(96, 165, 250, 0.6) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    position: relative;
}

/* 编辑高亮动画 - 简约脉动效果 */
body.dark-theme .message-bubble.highlight-edit {
    animation: editHighlightSimple 1.5s ease-in-out;
}

@keyframes editHighlightSimple {
    0%, 100% {
        border-color: rgba(96, 165, 250, 0.6);
    }
    50% {
        border-color: rgba(96, 165, 250, 0.8);
    }
}

body.dark-theme .user-message-container .message-bubble.highlight-edit {
    animation-name: highlightEditUserAnimDark;
}

body.dark-theme .assistant-message-container .message-bubble.highlight-edit {
    animation-name: highlightEditAnimDark;
}

@keyframes highlightEditAnimDark {
    0% {
        background: linear-gradient(135deg, rgba(245, 158, 11, 0.2) 0%, rgba(30, 41, 59, 0.95) 100%);
        border-color: rgba(245, 158, 11, 0.4);
    }
    50% {
        background: linear-gradient(135deg, rgba(245, 158, 11, 0.2) 0%, rgba(30, 41, 59, 0.95) 100%);
        border-color: rgba(245, 158, 11, 0.4);
    }
    100% {
        background: linear-gradient(135deg, rgba(30, 41, 59, 0.95) 0%, rgba(51, 65, 85, 0.9) 100%);
        border-color: var(--border-tertiary);
    }
}

@keyframes highlightEditUserAnimDark {
    0% {
        background: linear-gradient(135deg, rgba(59, 130, 246, 0.3) 0%, rgba(30, 41, 59, 0.95) 100%);
        border-color: rgba(59, 130, 246, 0.5);
    }
    50% {
        background: linear-gradient(135deg, rgba(59, 130, 246, 0.3) 0%, rgba(30, 41, 59, 0.95) 100%);
        border-color: rgba(59, 130, 246, 0.5);
    }
    100% {
        background: linear-gradient(135deg, rgba(99, 102, 241, 0.15) 0%, rgba(30, 41, 59, 0.95) 100%);
        border-color: var(--border-tertiary);
    }
}

/* 编辑输入框 - 简化版，继承明亮主题的统一定义 */
body.dark-theme .message-edit-input,
body.dark-theme .message-edit-textarea {
    background: rgba(51, 65, 85, 0.6);
    border-color: rgba(71, 85, 105, 0.4);
    color: var(--text-primary);
    /* 圆角、内边距、字体、行高、过渡、尺寸等样式继承明亮主题的统一定义 */
    resize: vertical;
    min-height: 80px;
    width: 100%;
    box-sizing: border-box;
}

body.dark-theme .message-edit-input:focus,
body.dark-theme .message-edit-textarea:focus {
    border-color: var(--border-focus);
    background: rgba(51, 65, 85, 0.8);
    /* 轮廓样式继承明亮主题的统一定义 */
}

body.dark-theme .message-edit-input::placeholder,
body.dark-theme .message-edit-textarea::placeholder {
    color: var(--text-muted);
    opacity: 0.8;
    /* 字体样式继承明亮主题的统一定义 */
}

/* 编辑工具栏 - 简化版，继承明亮主题的统一定义 */
body.dark-theme .message-edit-toolbar {
    background: linear-gradient(135deg,
        rgba(51, 65, 85, 0.8) 0%,
        rgba(30, 41, 59, 0.9) 100%);
    border-top-color: var(--border-tertiary);
    backdrop-filter: blur(8px);
    /* 内边距、布局、间距、圆角等样式继承明亮主题的统一定义 */
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: var(--spacing-sm);
}

/* 编辑操作按钮 - 简化版，继承明亮主题的统一定义 */
body.dark-theme .message-edit-save,
body.dark-theme .message-edit-cancel {
    /* 内边距、圆角、字体大小等样式继承明亮主题的统一定义 */
    font-weight: var(--font-weight-medium) !important;
    transition: all var(--duration-fast) var(--ease-out) !important;
    cursor: pointer;
    border: 1px solid transparent;
}

/* 保存按钮 */
body.dark-theme .message-edit-save {
    background: linear-gradient(135deg, var(--color-success) 0%, #059669 100%) !important;
    color: white !important;
    border-color: var(--color-success) !important;
}

body.dark-theme .message-edit-save:hover:not(:disabled) {
    background: #059669 !important;
    border-color: #059669 !important;
}

/* 取消按钮 - 简约设计 */
body.dark-theme .message-edit-cancel {
    background: rgba(71, 85, 105, 0.6) !important;
    color: var(--text-secondary) !important;
    border-color: rgba(71, 85, 105, 0.4) !important;
}

body.dark-theme .message-edit-cancel:hover {
    background: rgba(71, 85, 105, 0.8) !important;
    color: var(--text-primary) !important;
}

/* === 15. 对话设置界面优化 === */

/* 对话选项栏 - 简约清新设计 */
body.dark-theme #conversation-options-bar {
    height: 48px;
    background: rgba(30, 41, 59, 0.8) !important;
    border-bottom: 1px solid rgba(71, 85, 105, 0.3) !important;
    display: flex;
    align-items: center;
    padding: 0 15px !important; /* 统一内边距，与白天主题一致 */
    width: 100%;
    z-index: calc(var(--z-sticky) - 10);
    flex-shrink: 0;
    gap: 11.25px !important; /* 统一间距，与白天主题一致 */
    position: relative;
}

body.dark-theme #conversation-options-bar label {
    font-size: var(--font-size-sm) !important;
    font-weight: var(--font-weight-medium) !important;
    color: var(--text-secondary) !important;
    flex-shrink: 0;
    margin-right: var(--spacing-xs) !important;
    line-height: var(--line-height-tight) !important;
}

/* 对话设置按钮 - 简约设计 */
body.dark-theme #conversation-settings-button {
    background: rgba(51, 65, 85, 0.6) !important;
    border: 1px solid rgba(71, 85, 105, 0.4) !important;
    color: var(--text-secondary) !important;
    border-radius: var(--radius-base) !important;
    padding: var(--spacing-sm) var(--spacing-md) !important;
    transition: all 0.2s ease !important;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

body.dark-theme #conversation-settings-button:hover {
    background: rgba(51, 65, 85, 0.8) !important;
    border-color: rgba(96, 165, 250, 0.5) !important;
    color: var(--text-primary) !important;
}

body.dark-theme #conversation-settings-button:focus-visible {
    outline: 2px solid var(--border-focus) !important;
    outline-offset: 2px !important;
}

/* 对话设置模态框 */
body.dark-theme #conversation-settings-modal .modal-content {
    background: linear-gradient(135deg,
        rgba(30, 41, 59, 0.95) 0%,
        rgba(15, 23, 42, 0.98) 100%) !important;
    border: 1px solid var(--border-primary) !important;
    border-radius: var(--radius-xl) !important;
    box-shadow: var(--shadow-2xl) !important;
    backdrop-filter: blur(20px) saturate(1.2) !important;
    max-width: 600px;
    width: 90%;
}

/* 对话设置表单组 */
body.dark-theme #conversation-settings-modal .form-group {
    margin-bottom: var(--spacing-lg) !important;
}

body.dark-theme #conversation-settings-modal .form-group label {
    color: var(--text-secondary) !important;
    font-weight: var(--font-weight-medium) !important;
    margin-bottom: var(--spacing-sm) !important;
    display: block;
}

/* 对话标题输入框 - 简约设计 */
body.dark-theme #conversation-title-input {
    background: rgba(51, 65, 85, 0.6) !important;
    border: 1px solid rgba(71, 85, 105, 0.4) !important;
    color: var(--text-primary) !important;
    border-radius: var(--radius-base) !important;
    padding: var(--spacing-md) !important;
    width: 100%;
    font-size: var(--font-size-base) !important;
    transition: all 0.2s ease !important;
}

body.dark-theme #conversation-title-input:focus {
    border-color: var(--border-focus) !important;
    background: rgba(51, 65, 85, 0.8) !important;
    outline: none !important;
}

body.dark-theme #conversation-title-input::placeholder {
    color: var(--text-muted) !important;
    opacity: 0.8 !important;
    font-style: italic !important;
}

/* 系统提示输入框 - 增强视觉效果 */
body.dark-theme #conversation-system-prompt-input {
    background: linear-gradient(135deg,
        rgba(71, 85, 105, 0.9) 0%,
        rgba(51, 65, 85, 0.95) 100%) !important;
    border: 1.5px solid rgba(71, 85, 105, 0.6) !important;
    color: var(--text-primary) !important;
    border-radius: var(--radius-base) !important;
    padding: var(--spacing-md) !important;
    width: 100%;
    min-height: 120px;
    font-family: var(--font-family-base) !important;
    font-size: var(--font-size-base) !important;
    line-height: var(--line-height-normal) !important;
    resize: vertical;
    transition: all var(--duration-fast) var(--ease-out) !important;
    backdrop-filter: blur(4px) !important;
    box-shadow:
        0 2px 8px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.05) !important;
}

body.dark-theme #conversation-system-prompt-input:focus {
    border-color: var(--border-focus) !important;
    background: linear-gradient(135deg,
        rgba(59, 130, 246, 0.1) 0%,
        rgba(71, 85, 105, 0.95) 100%) !important;
    box-shadow: var(--shadow-focus) !important;
    outline: none !important;
}

body.dark-theme #conversation-system-prompt-input::placeholder {
    color: var(--text-muted) !important;
    opacity: 0.8 !important;
    font-style: italic !important;
}

/* === 16. 自定义选择器系统优化 === */

/* 选择器容器 */
body.dark-theme .custom-selector-container {
    position: relative;
    flex-grow: 1;
    min-width: 150px;
    max-width: 100%;
}

/* 选择器触发器 */
body.dark-theme .custom-selector-trigger {
    height: 38px;
    width: 100%;
    padding: 5.625px 11.25px !important; /* 统一内边距，与白天主题保持一致 */
    background: linear-gradient(135deg,
        rgba(71, 85, 105, 0.9) 0%,
        rgba(51, 65, 85, 0.95) 100%) !important;
    border: 1px solid var(--border-secondary) !important;
    color: var(--text-primary) !important;
    border-radius: var(--radius-base) !important;
    font-size: var(--font-size-sm) !important;
    font-weight: var(--font-weight-normal) !important;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--spacing-xs);
    transition: all var(--duration-fast) var(--ease-out) !important;
    backdrop-filter: blur(8px) !important;
}

body.dark-theme .custom-selector-trigger:hover {
    border-color: var(--border-primary) !important;
    background: linear-gradient(135deg,
        rgba(96, 165, 250, 0.1) 0%,
        rgba(71, 85, 105, 0.95) 100%) !important;
    transform: translateY(-1px) !important;
    box-shadow: var(--shadow-sm) !important;
}

body.dark-theme .custom-selector-trigger.active,
body.dark-theme .custom-selector-trigger:focus {
    border-color: var(--border-focus) !important;
    background: linear-gradient(135deg,
        rgba(59, 130, 246, 0.15) 0%,
        rgba(71, 85, 105, 0.95) 100%) !important;
    box-shadow: var(--shadow-focus) !important;
    outline: none !important;
}

body.dark-theme .custom-selector-trigger:disabled {
    background: linear-gradient(135deg,
        rgba(71, 85, 105, 0.5) 0%,
        rgba(51, 65, 85, 0.6) 100%) !important;
    color: var(--text-muted) !important;
    cursor: not-allowed;
    opacity: 0.6;
}

/* 选择器显示值 */
body.dark-theme .custom-selector-trigger .selected-value-display {
    flex-grow: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: left;
    color: inherit;
    font-size: inherit;
}

/* 下拉箭头 */
body.dark-theme .custom-selector-trigger .fa-chevron-down {
    font-size: 0.7em !important;
    color: var(--text-tertiary) !important;
    flex-shrink: 0;
    transition: transform var(--duration-fast) var(--ease-out) !important;
}

body.dark-theme .custom-selector-trigger.active .fa-chevron-down {
    transform: rotate(180deg) !important;
    color: var(--text-secondary) !important;
}

/* 选择器面板 */
body.dark-theme .custom-selector-panel {
    position: absolute;
    top: calc(100% + 4px);
    left: 0;
    right: 0;
    background: linear-gradient(135deg,
        rgba(30, 41, 59, 0.98) 0%,
        rgba(15, 23, 42, 0.95) 100%) !important;
    border: 1px solid var(--border-primary) !important;
    border-radius: var(--radius-base) !important;
    box-shadow: var(--shadow-xl) !important;
    backdrop-filter: blur(20px) saturate(1.2) !important;
    z-index: calc(var(--z-dropdown) + 10);
    max-height: 300px;
    overflow-y: auto;
    display: none;
    opacity: 0;
    transform: translateY(-8px);
    transition: opacity var(--duration-fast) var(--ease-out),
                transform var(--duration-fast) var(--ease-out) !important;
}

body.dark-theme .custom-selector-panel.visible {
    display: block;
    opacity: 1;
    transform: translateY(0);
}

/* 选择器选项 */
body.dark-theme .custom-selector-option {
    padding: var(--spacing-sm) var(--spacing-md) !important;
    font-size: var(--font-size-sm) !important;
    cursor: pointer;
    color: var(--text-secondary) !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    display: flex;
    align-items: center;
    justify-content: space-between;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    transition: all var(--duration-fast) var(--ease-out) !important;
}

body.dark-theme .custom-selector-option:last-child {
    border-bottom: none;
}

body.dark-theme .custom-selector-option:hover {
    background: linear-gradient(135deg,
        rgba(96, 165, 250, 0.15) 0%,
        rgba(59, 130, 246, 0.1) 100%) !important;
    color: var(--text-primary) !important;
}

body.dark-theme .custom-selector-option.selected {
    background: linear-gradient(135deg,
        rgba(59, 130, 246, 0.2) 0%,
        rgba(37, 99, 235, 0.15) 100%) !important;
    color: var(--accent-blue) !important;
    font-weight: var(--font-weight-medium) !important;
}

body.dark-theme .custom-selector-option.selected:hover {
    background: linear-gradient(135deg,
        rgba(59, 130, 246, 0.25) 0%,
        rgba(37, 99, 235, 0.2) 100%) !important;
}

/* 特殊选项 */
body.dark-theme .custom-selector-option[data-value="add_custom"] {
    margin-top: var(--spacing-xs);
    padding-top: var(--spacing-sm);
    color: var(--color-primary) !important;
    border-top: 1px solid var(--border-tertiary);
    font-weight: var(--font-weight-medium);
}

/* 选项组标签 */
body.dark-theme .custom-selector-optgroup-label {
    padding: var(--spacing-sm) var(--spacing-md) var(--spacing-xs) var(--spacing-md) !important;
    font-size: var(--font-size-xs) !important;
    color: var(--text-muted) !important;
    font-weight: var(--font-weight-semibold) !important;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    background: rgba(255, 255, 255, 0.02);
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    cursor: default;
}

/* 删除按钮 */
body.dark-theme .custom-model-option-delete {
    color: var(--color-danger) !important;
    background: none;
    border: none;
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    opacity: 0;
    transition: all var(--duration-fast) var(--ease-out) !important;
}

body.dark-theme .custom-selector-option:hover .custom-model-option-delete {
    opacity: 1;
}

body.dark-theme .custom-model-option-delete:hover {
    background: rgba(239, 68, 68, 0.1) !important;
    color: #fca5a5 !important;
}

/* === 17. API配置管理界面优化 === */

/* API设置模态框 - 简约清新设计 */
body.dark-theme #settings-modal .modal-content {
    background: rgba(30, 41, 59, 0.95) !important;
    border: 1px solid rgba(71, 85, 105, 0.3) !important;
    border-radius: var(--radius-xl) !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2) !important;
    max-width: 700px;
    width: 90%;
}

/* 模态框头部 */
body.dark-theme .modal-header {
    padding: var(--spacing-xl) var(--spacing-xl) var(--spacing-lg) var(--spacing-xl) !important;
    border-bottom: 1px solid var(--border-secondary) !important;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-shrink: 0;
    background: linear-gradient(135deg,
        rgba(51, 65, 85, 0.8) 0%,
        rgba(30, 41, 59, 0.9) 100%) !important;
    border-radius: var(--radius-xl) var(--radius-xl) 0 0 !important;
}

body.dark-theme .modal-header h2 {
    color: var(--text-primary) !important;
    font-weight: var(--font-weight-semibold) !important;
    margin: 0;
}

/* 模态框关闭按钮 */
body.dark-theme .modal-close-button {
    background: none !important;
    border: none !important;
    color: var(--text-tertiary) !important;
    font-size: 1.5em !important;
    cursor: pointer;
    padding: var(--spacing-xs) !important;
    border-radius: var(--radius-sm) !important;
    transition: all var(--duration-fast) var(--ease-out) !important;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

body.dark-theme .modal-close-button:hover {
    background: rgba(239, 68, 68, 0.1);
    color: var(--color-danger);
    /* 变换效果继承明亮主题的统一定义 */
}

/* 配置选择器 - 简化版，继承明亮主题的统一定义 */
body.dark-theme .modal-profile-selector {
    border-bottom-color: var(--border-primary);
    /* 布局、间距、内边距等样式继承明亮主题的统一定义 */
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    align-items: center;
    padding-bottom: 15px;
    flex-shrink: 0;
    background: transparent;
    border-radius: 0;
    border-left: none;
    border-right: none;
    border-top: none;
    padding-left: 0;
    padding-right: 0;
    padding-top: 0;
}

body.dark-theme .modal-profile-selector label {
    color: var(--text-secondary);
    font-weight: 500;
    /* 边距、行高、收缩等样式继承明亮主题的统一定义 */
    margin-bottom: 0;
    line-height: 38px;
    flex-shrink: 0;
}

/* 配置选择器容器 - 简化版，继承明亮主题的统一定义 */
body.dark-theme #settings-profile-selector-container {
    /* 布局和尺寸样式继承明亮主题的统一定义 */
    flex-grow: 1;
    min-width: 180px;
}

/* 模态框表单区域 - 简化版，继承明亮主题的统一定义 */
body.dark-theme .modal-form-area {
    /* 内边距、高度、滚动、布局等样式继承明亮主题的统一定义 */
    max-height: 60vh;
    overflow-y: auto;
    flex-grow: 1;
}

/* 表单组样式继承明亮主题的统一定义 */

body.dark-theme .modal-form-area .form-group label {
    color: var(--text-secondary);
    font-weight: var(--font-weight-medium);
    /* 边距、显示、字体大小等样式继承明亮主题的统一定义 */
    display: block;
}

/* 表单输入框 - 简化版，继承明亮主题的统一定义 */
body.dark-theme .modal-form-area input[type="text"],
body.dark-theme .modal-form-area input[type="password"],
body.dark-theme .modal-form-area input[type="number"],
body.dark-theme .modal-form-area input[type="url"],
body.dark-theme .modal-form-area textarea {
    background: rgba(51, 65, 85, 0.6);
    border-color: rgba(71, 85, 105, 0.4);
    color: var(--text-primary);
    /* 圆角、内边距、字体、过渡、尺寸等样式继承明亮主题的统一定义 */
    width: 100%;
    box-sizing: border-box;
}

body.dark-theme .modal-form-area input:focus,
body.dark-theme .modal-form-area textarea:focus {
    border-color: var(--border-focus);
    background: rgba(51, 65, 85, 0.8);
    /* 轮廓样式继承明亮主题的统一定义 */
}

body.dark-theme .modal-form-area input::placeholder,
body.dark-theme .modal-form-area textarea::placeholder {
    color: var(--text-muted);
    opacity: 0.8;
    /* 字体样式继承明亮主题的统一定义 */
}

/* 输入验证状态 */
body.dark-theme .form-group.invalid input,
body.dark-theme .form-group.invalid textarea,
body.dark-theme .form-group.invalid .custom-selector-trigger {
    border-color: var(--color-danger) !important;
    background: linear-gradient(135deg,
        rgba(239, 68, 68, 0.1) 0%,
        rgba(71, 85, 105, 0.95) 100%) !important;
}

body.dark-theme .invalid-feedback {
    display: none;
    width: 100%;
    margin-top: var(--spacing-xs) !important;
    font-size: var(--font-size-sm) !important;
    color: var(--color-danger) !important;
}

body.dark-theme .form-group.invalid .invalid-feedback {
    display: block;
}

/* 模态框操作区域 */
body.dark-theme .modal-actions {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: var(--spacing-xl) !important;
    border-top: 1px solid var(--border-secondary) !important;
    padding: var(--spacing-lg) var(--spacing-xl) !important;
    gap: var(--spacing-md);
    flex-shrink: 0;
    background: linear-gradient(135deg,
        rgba(51, 65, 85, 0.6) 0%,
        rgba(30, 41, 59, 0.8) 100%) !important;
    border-radius: 0 0 var(--radius-xl) var(--radius-xl) !important;
}

body.dark-theme .modal-action-row {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: var(--spacing-md);
    width: 100%;
}

/* === 18. 绘画界面完整黑夜主题适配 === */

/* 绘画模态框 - 简约清新设计 */
body.dark-theme #image-gen-modal .modal-content {
    background: rgba(30, 41, 59, 0.95) !important;
    border: 1px solid rgba(71, 85, 105, 0.3) !important;
    border-radius: 16px !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2) !important;
    max-width: 680px;
    width: 92%;
    max-height: 92vh;
    overflow-y: auto;
}

/* 绘画模态框表单区域 */
body.dark-theme #image-gen-modal .modal-form-area {
    overflow-x: hidden;
    padding: 24px;
    background: transparent !important;
}

/* 绘画界面标题 */
body.dark-theme #image-gen-modal h2 {
    color: var(--text-primary) !important;
    text-align: center;
    margin-bottom: 25px;
    font-weight: 500;
    font-size: 1.2em;
}

/* 现代化的选项容器 */
body.dark-theme #image-gen-options {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-top: 20px;
}

/* 卡片式分组布局 - 简约设计 */
body.dark-theme .image-gen-card {
    background: rgba(51, 65, 85, 0.4) !important;
    border: 1px solid rgba(71, 85, 105, 0.3) !important;
    border-radius: 12px;
    padding: 16px;
    transition: all 0.2s ease;
}

body.dark-theme .image-gen-card:hover {
    background: rgba(51, 65, 85, 0.6) !important;
    border-color: rgba(96, 165, 250, 0.4) !important;
}

body.dark-theme .image-gen-card-title {
    font-size: 0.85em;
    font-weight: 600;
    color: var(--text-secondary) !important;
    margin-bottom: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* 精致的表单组件设计 - 黑夜主题 */
body.dark-theme .image-gen-form-group {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

body.dark-theme .image-gen-form-group label {
    font-size: 0.8em;
    font-weight: 500;
    color: var(--text-tertiary) !important;
    margin: 0;
    text-transform: uppercase;
    letter-spacing: 0.3px;
}

/* 现代化输入框样式 - 简约设计 */
body.dark-theme .image-gen-input {
    padding: 10px 12px;
    border: 1px solid rgba(71, 85, 105, 0.4) !important;
    border-radius: 8px;
    font-size: 0.9em;
    background: rgba(51, 65, 85, 0.6) !important;
    color: var(--text-primary) !important;
    transition: all 0.2s ease;
    height: 40px;
    box-sizing: border-box;
}

/* body.dark-theme .image-gen-input:focus 样式已统一到全局聚焦效果系统中 */

body.dark-theme .image-gen-input:disabled {
    background: rgba(51, 65, 85, 0.5) !important;
    color: var(--text-muted) !important;
    cursor: not-allowed;
    opacity: 0.6;
}

/* 自适应文本域样式 - 简约设计 */
body.dark-theme .image-gen-textarea {
    padding: 12px;
    border: 1px solid rgba(71, 85, 105, 0.4) !important;
    border-radius: 8px;
    font-size: 0.9em;
    background: rgba(51, 65, 85, 0.6) !important;
    color: var(--text-primary) !important;
    transition: all 0.2s ease;
    width: 100%;
    min-height: 80px;
    max-height: 200px;
    resize: vertical;
    font-family: inherit;
    line-height: 1.5;
}

body.dark-theme .image-gen-textarea:focus {
    border-color: var(--border-focus) !important;
    background: rgba(51, 65, 85, 0.8) !important;
    outline: none;
}

body.dark-theme .image-gen-textarea::placeholder {
    color: var(--text-muted) !important;
    font-style: italic;
    opacity: 0.8;
}

/* 现代化选择器样式 - 黑夜主题 */
body.dark-theme .image-gen-selector {
    position: relative;
    width: 100%;
    max-width: 160px;
}

body.dark-theme .image-gen-selector .custom-selector-trigger {
    width: 100%;
    height: 40px;
    padding: 10px 12px;
    border: 1.5px solid rgba(71, 85, 105, 0.6) !important;
    border-radius: 8px;
    background: linear-gradient(135deg,
        rgba(71, 85, 105, 0.9) 0%,
        rgba(51, 65, 85, 0.95) 100%) !important;
    color: var(--text-primary) !important;
    font-size: 0.9em;
    transition: all 0.2s ease;
    cursor: pointer;
    backdrop-filter: blur(4px) !important;
    box-shadow:
        0 2px 8px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.05) !important;
}

body.dark-theme .image-gen-selector .custom-selector-trigger:hover {
    border-color: rgba(96, 165, 250, 0.5) !important;
    background: linear-gradient(135deg,
        rgba(96, 165, 250, 0.08) 0%,
        rgba(71, 85, 105, 0.95) 100%) !important;
}

body.dark-theme .image-gen-selector .custom-selector-trigger:focus {
    border-color: var(--border-focus) !important;
    box-shadow:
        0 0 0 3px rgba(59, 130, 246, 0.15),
        0 4px 16px rgba(59, 130, 246, 0.1),
        0 2px 8px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.08) !important;
    outline: none;
}

/* 现代化切换开关设计 - 简约风格 */
body.dark-theme .image-gen-toggle-group {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 14px;
    background: rgba(51, 65, 85, 0.4) !important;
    border: 1px solid rgba(71, 85, 105, 0.3) !important;
    border-radius: 8px;
    transition: all 0.2s ease;
    cursor: pointer;
    min-height: 40px;
    box-sizing: border-box;
}

body.dark-theme .image-gen-toggle-group:hover {
    background: rgba(51, 65, 85, 0.6) !important;
    border-color: rgba(96, 165, 250, 0.4) !important;
}

body.dark-theme .image-gen-toggle-group.checked {
    background: rgba(59, 130, 246, 0.15) !important;
    border-color: var(--color-primary) !important;
}

body.dark-theme .image-gen-toggle-group label {
    margin: 0;
    cursor: pointer;
    font-size: 0.85em;
    font-weight: 500;
    color: var(--text-secondary) !important;
    user-select: none;
}

body.dark-theme .image-gen-toggle-group.checked label {
    color: var(--text-primary) !important;
}

body.dark-theme .image-gen-toggle-group input[type="checkbox"] {
    width: 16px;
    height: 16px;
    cursor: pointer;
    accent-color: var(--color-primary);
    background: rgba(71, 85, 105, 0.9) !important;
    border: 1px solid rgba(71, 85, 105, 0.6) !important;
}

/* 现代化图像生成按钮设计 - 简约风格 */
body.dark-theme .image-gen-action-button {
    padding: 8px 16px;
    border: 1px solid transparent;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    line-height: 20px;
}

body.dark-theme .image-gen-action-button.primary {
    background: var(--color-primary) !important;
    color: white !important;
    border-color: var(--color-primary) !important;
}

body.dark-theme .image-gen-action-button.primary:hover {
    background: var(--color-primary-hover) !important;
    border-color: var(--color-primary-hover) !important;
}

body.dark-theme .image-gen-action-button.secondary {
    background: rgba(71, 85, 105, 0.6) !important;
    color: var(--text-secondary) !important;
    border-color: rgba(71, 85, 105, 0.4) !important;
}

body.dark-theme .image-gen-action-button.secondary:hover {
    background: rgba(71, 85, 105, 0.8) !important;
    color: var(--text-primary) !important;
}

/* 随机种子按钮特殊样式 - 黑夜主题 */
body.dark-theme #image-gen-random-seed-button {
    width: 40px;
    height: 40px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 8px;
    background: linear-gradient(135deg,
        rgba(71, 85, 105, 0.9) 0%,
        rgba(51, 65, 85, 0.95) 100%);
    border-color: rgba(71, 85, 105, 0.6);
    color: var(--text-secondary);
    backdrop-filter: blur(4px);
    box-shadow:
        0 2px 8px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.05);
    /* 圆角、光标、过渡等样式继承明亮主题的统一定义 */
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
}

body.dark-theme #image-gen-random-seed-button:hover {
    background: linear-gradient(135deg,
        rgba(96, 165, 250, 0.15) 0%,
        rgba(71, 85, 105, 0.95) 100%);
    border-color: rgba(96, 165, 250, 0.5);
    color: var(--text-primary);
    box-shadow:
        0 4px 16px rgba(96, 165, 250, 0.1),
        0 2px 8px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.08);
    /* 变换效果继承明亮主题的统一定义 */
    transform: translateY(-1px);
}

body.dark-theme #image-gen-random-seed-button i {
    font-size: 0.9em;
}

/* 提示词操作按钮优化 - 简约风格 */
body.dark-theme #image-gen-prompt-actions {
    display: flex;
    gap: 8px;
    margin-top: 8px;
    flex-wrap: wrap;
}

body.dark-theme #image-gen-prompt-actions button {
    border-color: rgba(71, 85, 105, 0.4);
    background: rgba(51, 65, 85, 0.4);
    color: var(--text-secondary);
    /* 内边距、高度、字体、圆角、过渡、布局、间距、字重、光标等样式继承明亮主题的统一定义 */
    padding: 6px 12px;
    height: 32px;
    font-size: 0.8em;
    border-radius: 6px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 4px;
    font-weight: 500;
    cursor: pointer;
}

body.dark-theme #image-gen-prompt-actions button:hover:not(:disabled) {
    background: rgba(51, 65, 85, 0.6);
    border-color: rgba(96, 165, 250, 0.5);
    color: var(--text-primary);
}

body.dark-theme #image-gen-prompt-actions button:disabled {
    background: rgba(51, 65, 85, 0.3);
    color: var(--text-muted);
    /* 光标和透明度样式继承明亮主题的统一定义 */
    cursor: not-allowed;
    opacity: 0.6;
}

body.dark-theme #image-gen-prompt-actions button i {
    font-size: 0.85em;
}

/* 特定按钮颜色 - 简化版，继承明亮主题的统一定义 */
body.dark-theme #enhance-prompt-button {
    background: rgba(59, 130, 246, 0.15);
    border-color: rgba(59, 130, 246, 0.4);
    color: #93c5fd;
}

body.dark-theme #enhance-prompt-button:hover:not(:disabled) {
    background: rgba(59, 130, 246, 0.25);
    border-color: rgba(59, 130, 246, 0.6);
    color: #bfdbfe;
}

/* 图像预览容器 - 简化版，继承明亮主题的统一定义 */
body.dark-theme #image-gen-preview-container {
    background: linear-gradient(135deg,
        rgba(30, 41, 59, 0.8) 0%,
        rgba(15, 23, 42, 0.9) 100%);
    border-color: rgba(71, 85, 105, 0.4);
    backdrop-filter: blur(12px);
    /* 边距、内边距、圆角等样式继承明亮主题的统一定义 */
    margin-top: 24px;
    padding: 20px;
    border-radius: 12px;
    box-shadow:
        0 4px 16px rgba(0, 0, 0, 0.15),
        0 1px 3px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.05) !important;
}

body.dark-theme .preview-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;
    color: var(--text-secondary) !important;
    font-size: 0.9em;
    font-weight: 500;
}

body.dark-theme .preview-header i {
    color: var(--color-primary) !important;
    font-size: 1.1em;
}

/* === 19. TTS语音选择器优化 === */

/* TTS语音选择器容器 */
body.dark-theme .tts-voice-selector-container {
    position: relative;
    width: 100%;
}

/* TTS语音选择器 */
body.dark-theme .tts-voice-selector {
    background: linear-gradient(135deg,
        rgba(71, 85, 105, 0.9) 0%,
        rgba(51, 65, 85, 0.95) 100%) !important;
    border: 1px solid var(--border-secondary) !important;
    color: var(--text-primary) !important;
    border-radius: var(--radius-base) !important;
    padding: var(--spacing-md) !important;
    width: 100%;
    font-size: var(--font-size-base) !important;
    transition: all var(--duration-fast) var(--ease-out) !important;
    cursor: pointer;
    appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23cbd5e1' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right var(--spacing-md) center;
    background-size: 16px;
    padding-right: calc(var(--spacing-md) + 24px);
}

body.dark-theme .tts-voice-selector:focus {
    border-color: var(--border-focus) !important;
    background: linear-gradient(135deg,
        rgba(59, 130, 246, 0.1) 0%,
        rgba(71, 85, 105, 0.95) 100%) !important;
    box-shadow: var(--shadow-focus) !important;
    outline: none !important;
}

body.dark-theme .tts-voice-selector:hover {
    border-color: var(--border-primary) !important;
    background: linear-gradient(135deg,
        rgba(96, 165, 250, 0.1) 0%,
        rgba(71, 85, 105, 0.95) 100%) !important;
}

/* TTS语音选择器选项 */
body.dark-theme .tts-voice-selector option {
    background: rgba(30, 41, 59, 0.95) !important;
    color: var(--text-primary) !important;
    padding: var(--spacing-sm) !important;
}

/* TTS测试按钮 */
body.dark-theme .tts-test-button {
    background: linear-gradient(135deg,
        rgba(16, 185, 129, 0.9) 0%,
        rgba(5, 150, 105, 1) 100%) !important;
    border: 1px solid rgba(16, 185, 129, 0.3) !important;
    color: white !important;
    border-radius: var(--radius-base) !important;
    padding: var(--spacing-sm) var(--spacing-md) !important;
    font-size: var(--font-size-sm) !important;
    font-weight: var(--font-weight-medium) !important;
    cursor: pointer;
    transition: all var(--duration-fast) var(--ease-out) !important;
    margin-top: var(--spacing-sm);
}

body.dark-theme .tts-test-button:hover:not(:disabled) {
    background: linear-gradient(135deg,
        rgba(5, 150, 105, 1) 0%,
        rgba(4, 120, 87, 1) 100%) !important;
    border-color: rgba(5, 150, 105, 0.5) !important;
    transform: translateY(-1px) !important;
    box-shadow: var(--shadow-button) !important;
}

body.dark-theme .tts-test-button:disabled {
    background: linear-gradient(135deg,
        rgba(71, 85, 105, 0.5) 0%,
        rgba(51, 65, 85, 0.6) 100%) !important;
    color: var(--text-muted) !important;
    cursor: not-allowed;
    opacity: 0.6;
}

/* === 19. 响应式设计优化 === */

/* 平板设备适配 */
@media (max-width: 1024px) {
    body.dark-theme .modal-content {
        max-width: 85% !important;
        margin: var(--spacing-lg) auto !important;
    }

    body.dark-theme .custom-selector-panel {
        max-height: 250px !important;
    }

    body.dark-theme .modal-form-area {
        max-height: 50vh !important;
    }
}

/* 移动设备适配 */
@media (max-width: 768px) {
    body.dark-theme .modal-content {
        max-width: 95% !important;
        margin: var(--spacing-md) auto !important;
        border-radius: var(--radius-lg) !important;
    }

    body.dark-theme .modal-header,
    body.dark-theme .modal-form-area,
    body.dark-theme .modal-actions {
        padding-left: var(--spacing-lg) !important;
        padding-right: var(--spacing-lg) !important;
    }

    body.dark-theme .custom-selector-panel {
        max-height: 200px !important;
    }

    body.dark-theme .modal-action-row {
        flex-direction: column !important;
        align-items: stretch !important;
    }

    body.dark-theme .modal-form-area {
        max-height: 40vh !important;
    }

    body.dark-theme #conversation-options-bar {
        padding: 0 11.25px !important; /* 保持与白天主题一致的间距比例 */
        gap: 7.5px !important; /* 小屏幕下适当缩小间距 */
    }

    body.dark-theme .custom-selector-container {
        min-width: 120px !important;
    }
}

/* 小屏幕设备适配 */
@media (max-width: 480px) {
    body.dark-theme .modal-content {
        max-width: 98% !important;
        margin: var(--spacing-sm) auto !important;
    }

    body.dark-theme .modal-header h2 {
        font-size: var(--font-size-lg) !important;
    }

    body.dark-theme .custom-selector-trigger {
        height: 36px !important;
        font-size: var(--font-size-xs) !important;
    }

    body.dark-theme .custom-selector-panel {
        max-height: 180px !important;
    }

    body.dark-theme .modal-form-area {
        max-height: 35vh !important;
    }
}

/* === 20. 无障碍访问优化 === */

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    body.dark-theme .custom-selector-trigger,
    body.dark-theme .modal-form-area input,
    body.dark-theme .modal-form-area textarea {
        border-width: 2px !important;
    }

    body.dark-theme .custom-selector-option:hover,
    body.dark-theme .custom-selector-option.selected {
        background: rgba(59, 130, 246, 0.4) !important;
    }
}

/* 减少动画偏好支持 */
@media (prefers-reduced-motion: reduce) {
    body.dark-theme .custom-selector-trigger,
    body.dark-theme .custom-selector-panel,
    body.dark-theme .custom-selector-option,
    body.dark-theme .scroll-button,
    body.dark-theme .modal-form-area input,
    body.dark-theme .modal-form-area textarea {
        transition: none !important;
        animation: none !important;
    }

    body.dark-theme .scroll-button:hover {
        transform: none !important;
    }
}

/* 焦点可见性增强 */
body.dark-theme .custom-selector-trigger:focus-visible,
body.dark-theme .modal-form-area input:focus-visible,
body.dark-theme .modal-form-area textarea:focus-visible {
    outline: 3px solid var(--border-focus) !important;
    outline-offset: 2px !important;
}

/* 滚动按钮移除聚焦框 */
body.dark-theme .scroll-button:focus-visible {
    outline: none !important;
}

/* === 文件结束 === */